<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>视频镜像与倒放工具</title>
  <style>
    body { font-family: sans-serif; padding: 20px; }
    canvas { border: 1px solid #ccc; display: block; margin-top: 10px; }
    video { display: none; } /* 隐藏原始video元素 */
  </style>
</head>
<body>

<h2>上传视频并设置镜像或倒放</h2>
<input type="file" accept="video/*" id="fileInput">
<br><br>
<label><input type="checkbox" id="mirror"> 镜像</label>
<label><input type="checkbox" id="reverse"> 倒放</label>
<button id="playBtn">播放</button>

<canvas id="canvas" width="640" height="360"></canvas>
<video id="video" crossorigin="anonymous"></video>

<script>
const fileInput = document.getElementById('fileInput');
const video = document.getElementById('video');
const canvas = document.getElementById('canvas');
const ctx = canvas.getContext('2d');
const mirrorCheckbox = document.getElementById('mirror');
const reverseCheckbox = document.getElementById('reverse');
const playBtn = document.getElementById('playBtn');

let frameBuffer = []; // 用于倒放
let drawing = false;
let reverseMode = false;

fileInput.onchange = (e) => {
  const file = e.target.files[0];
  if (!file) return;

  const url = URL.createObjectURL(file);
  video.src = url;
  video.load();

  video.onloadedmetadata = () => {
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
  };
};

playBtn.onclick = async () => {
  if (!video.src) return;

  reverseMode = reverseCheckbox.checked;
  if (reverseMode) {
    await captureFrames(); // 预加载所有帧
    playReversed();
  } else {
    video.currentTime = 0;
    video.play();
    drawing = true;
    drawLoop();
  }
};

// 实时绘制视频帧
function drawLoop() {
  if (!drawing || video.paused || video.ended) return;

  ctx.save();
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  if (mirrorCheckbox.checked) {
    ctx.translate(canvas.width, 0);
    ctx.scale(-1, 1);
  }

  ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
  ctx.restore();

  requestAnimationFrame(drawLoop);
}

// 倒放模式：缓存帧并按顺序播放
async function captureFrames() {
  frameBuffer = [];
  const fps = 30;
  const duration = video.duration;
  const totalFrames = Math.floor(duration * fps);

  video.pause();
  for (let i = 0; i < totalFrames; i++) {
    video.currentTime = i / fps;
    await waitForSeek(video);

    const frame = getFrame();
    frameBuffer.push(frame);
  }
}

function getFrame() {
  const tempCanvas = document.createElement('canvas');
  tempCanvas.width = canvas.width;
  tempCanvas.height = canvas.height;
  const tempCtx = tempCanvas.getContext('2d');
  tempCtx.drawImage(video, 0, 0, canvas.width, canvas.height);
  return tempCanvas;
}

async function playReversed() {
  drawing = false;
  for (let i = frameBuffer.length - 1; i >= 0; i--) {
    ctx.save();
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    if (mirrorCheckbox.checked) {
      ctx.translate(canvas.width, 0);
      ctx.scale(-1, 1);
    }

    ctx.drawImage(frameBuffer[i], 0, 0);
    ctx.restore();
    await sleep(1000 / 30); // 假设30fps
  }
}

function waitForSeek(videoEl) {
  return new Promise(resolve => {
    const handler = () => {
      videoEl.removeEventListener('seeked', handler);
      resolve();
    };
    videoEl.addEventListener('seeked', handler);
  });
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
</script>

</body>
</html>