import cv2
import pytesseract

def is_new_subtitle(prev_frame, curr_frame, subtitle_region):
    # 只检测字幕区域
    prev_sub_img = prev_frame[subtitle_region[0]:subtitle_region[1], subtitle_region[2]:subtitle_region[3]]
    curr_sub_img = curr_frame[subtitle_region[0]:subtitle_region[1], subtitle_region[2]:subtitle_region[3]]

    # OCR 识别，灰度图提升识别准确度
    prev_text = pytesseract.image_to_string(cv2.cvtColor(prev_sub_img, cv2.COLOR_BGR2GRAY), config='--psm 6')
    curr_text = pytesseract.image_to_string(cv2.cvtColor(curr_sub_img, cv2.COLOR_BGR2GRAY), config='--psm 6')

    return prev_text.strip() != curr_text.strip()

def frame_diff(prev_gray, curr_gray, threshold=5000):
    diff = cv2.absdiff(curr_gray, prev_gray)
    non_zero_count = cv2.countNonZero(diff)
    return non_zero_count > threshold

def main(video_path):
    cap = cv2.VideoCapture(video_path)
    ret, prev_frame = cap.read()
    if not ret:
        print("视频读取失败")
        return

    # 预定义字幕区域(y1, y2, x1, x2)，根据视频调整
    subtitle_region = (prev_frame.shape[0] - 120, prev_frame.shape[0], 0, prev_frame.shape[1])  # 底部120像素全宽

    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)

    frame_idx = 1
    while True:
        ret, frame = cap.read()
        if not ret:
            break

        curr_gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # 判断画面是否有大变化
        big_change = frame_diff(prev_gray, curr_gray)

        # 判断字幕是否有变化
        subtitle_change = is_new_subtitle(prev_frame, frame, subtitle_region)

        if big_change:
            print(f"[帧 {frame_idx}] 画面变化明显")
        elif subtitle_change:
            print(f"[帧 {frame_idx}] 字幕发生变化，但画面基本稳定")
        else:
            print(f"[帧 {frame_idx}] 无明显变化")

        prev_frame = frame
        prev_gray = curr_gray
        frame_idx += 1

    cap.release()

if __name__ == "__main__":
    video_file = "/Users/<USER>/Downloads/465867182677433622_副本.mp4"  # 替换为你的视频文件路径
    main(video_file)