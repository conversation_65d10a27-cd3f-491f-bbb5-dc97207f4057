import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
import os
import subprocess # 用于调用 ffmpeg
import threading # 用于在后台处理音频，避免GUI卡顿
import json # 用于解析 ffmpeg loudnorm 的输出
from pydub import AudioSegment
from pydub.effects import normalize as pydub_peak_normalize # pydub的normalize是峰值标准化
import numpy as np

# --- 全局配置和默认值 ---
DEFAULT_TARGET_LUFS = -15.0
DEFAULT_TARGET_TP = -1.5
DEFAULT_BGM_DUCKING_DB = -18.0 # BGM在VO下降低的音量 (dB)
DEFAULT_VO_PEAK_NORM_DBFS = -3.0 # 配音初步峰值标准化到 -3dBFS
DEFAULT_SFX_PEAK_NORM_DBFS = -12.0 # 音效默认峰值标准化到 -12dBFS

class AudioProcessorApp:
    def __init__(self, root_window):
        self.root = root_window
        self.root.title("音频自动混音与响度标准化工具")
        self.root.geometry("750x800") # 增加窗口高度以容纳新的音效控制

        # --- 文件选择区 ---
        file_frame = tk.LabelFrame(self.root, text="音频文件选择", padx=10, pady=10)
        file_frame.pack(padx=10, pady=10, fill="x")

        # 配音 (VO)
        tk.Label(file_frame, text="配音 (VO):").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.vo_path_entry = tk.Entry(file_frame, width=50)
        self.vo_path_entry.grid(row=0, column=1, padx=5, pady=5)
        tk.Button(file_frame, text="浏览...", command=lambda: self.browse_file(self.vo_path_entry, "选择配音文件")).grid(row=0, column=2, padx=5, pady=5)

        # 背景音乐 (BGM)
        tk.Label(file_frame, text="背景音乐 (BGM):").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.bgm_path_entry = tk.Entry(file_frame, width=50)
        self.bgm_path_entry.grid(row=1, column=1, padx=5, pady=5)
        tk.Button(file_frame, text="浏览...", command=lambda: self.browse_file(self.bgm_path_entry, "选择背景音乐文件")).grid(row=1, column=2, padx=5, pady=5)

        # 音效 (SFX) - 允许多个
        tk.Label(file_frame, text="音效 (SFX) 列表:").grid(row=2, column=0, sticky="nw", padx=5, pady=5)
        self.sfx_listbox = tk.Listbox(file_frame, width=50, height=4, selectmode=tk.EXTENDED)
        self.sfx_listbox.grid(row=2, column=1, padx=5, pady=5, sticky="ew")
        sfx_button_frame = tk.Frame(file_frame)
        sfx_button_frame.grid(row=2, column=2, padx=5, pady=5, sticky="n")
        tk.Button(sfx_button_frame, text="添加音效", command=self.add_sfx).pack(fill="x")
        tk.Button(sfx_button_frame, text="移除选中", command=self.remove_sfx).pack(fill="x")

        # 音效开始时间
        tk.Label(file_frame, text="音效开始时间 (毫秒):").grid(row=3, column=0, sticky="nw", padx=5, pady=5)
        self.sfx_times_text = tk.Text(file_frame, width=50, height=4)
        self.sfx_times_text.grid(row=3, column=1, padx=5, pady=5, sticky="ew")
        tk.Label(file_frame, text="示例: 2000\n5500\n10200", justify=tk.LEFT).grid(row=3, column=2, sticky="nw", padx=5, pady=5)

        # 音效音量控制
        tk.Label(file_frame, text="音效音量控制 (dB):").grid(row=4, column=0, sticky="nw", padx=5, pady=5)
        self.sfx_volume_text = tk.Text(file_frame, width=50, height=4)
        self.sfx_volume_text.grid(row=4, column=1, padx=5, pady=5, sticky="ew")
        tk.Label(file_frame, text="示例:\n-6 (相对音量)\npeak:-12 (目标峰值)\n0 (保持原音量)", justify=tk.LEFT).grid(row=4, column=2, sticky="nw", padx=5, pady=5)

        # --- 参数设置区 ---
        params_frame = tk.LabelFrame(self.root, text="混音与标准化参数", padx=10, pady=10)
        params_frame.pack(padx=10, pady=10, fill="x")

        tk.Label(params_frame, text="目标集成响度 (LUFS):").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.target_lufs_entry = tk.Entry(params_frame, width=10)
        self.target_lufs_entry.insert(0, str(DEFAULT_TARGET_LUFS))
        self.target_lufs_entry.grid(row=0, column=1, sticky="w", padx=5, pady=5)

        tk.Label(params_frame, text="目标真峰值 (dBTP):").grid(row=0, column=2, sticky="w", padx=5, pady=5)
        self.target_tp_entry = tk.Entry(params_frame, width=10)
        self.target_tp_entry.insert(0, str(DEFAULT_TARGET_TP))
        self.target_tp_entry.grid(row=0, column=3, sticky="w", padx=5, pady=5)

        tk.Label(params_frame, text="BGM闪避量 (dB):").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.bgm_ducking_entry = tk.Entry(params_frame, width=10)
        self.bgm_ducking_entry.insert(0, str(DEFAULT_BGM_DUCKING_DB))
        self.bgm_ducking_entry.grid(row=1, column=1, sticky="w", padx=5, pady=5)
        
        tk.Label(params_frame, text="VO峰值标准化 (dBFS):").grid(row=1, column=2, sticky="w", padx=5, pady=5)
        self.vo_peak_norm_entry = tk.Entry(params_frame, width=10)
        self.vo_peak_norm_entry.insert(0, str(DEFAULT_VO_PEAK_NORM_DBFS))
        self.vo_peak_norm_entry.grid(row=1, column=3, sticky="w", padx=5, pady=5)

        # --- 输出设置区 ---
        output_frame = tk.LabelFrame(self.root, text="输出文件", padx=10, pady=10)
        output_frame.pack(padx=10, pady=10, fill="x")
        tk.Label(output_frame, text="保存位置:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.output_path_entry = tk.Entry(output_frame, width=50)
        self.output_path_entry.grid(row=0, column=1, padx=5, pady=5)
        tk.Button(output_frame, text="选择保存位置...", command=self.browse_save_path).grid(row=0, column=2, padx=5, pady=5)

        # --- 控制与状态区 ---
        control_frame = tk.Frame(self.root, padx=10, pady=10)
        control_frame.pack(padx=10, pady=10, fill="x")
        self.process_button = tk.Button(control_frame, text="开始处理音频", command=self.start_processing, width=20, height=2)
        self.process_button.pack(side="left", padx=10)

        # --- 处理参数显示区 ---
        self.params_display_frame = tk.LabelFrame(self.root, text="处理参数详情", padx=10, pady=10)
        self.params_display_frame.pack(padx=10, pady=10, fill="x")
        self.params_display_text = scrolledtext.ScrolledText(self.params_display_frame, wrap=tk.WORD, height=8)
        self.params_display_text.pack(fill="both", expand=True)

        # --- 状态显示区 ---
        status_frame = tk.LabelFrame(self.root, text="处理状态与日志", padx=10, pady=10)
        status_frame.pack(padx=10, pady=10, fill="both", expand=True)
        self.status_text = scrolledtext.ScrolledText(status_frame, wrap=tk.WORD, height=10)
        self.status_text.pack(fill="both", expand=True)

    def log_message(self, message):
        """安全地向状态文本框记录消息 (可从线程调用)"""
        self.status_text.insert(tk.END, message + "\n")
        self.status_text.see(tk.END) # 滚动到底部
        self.root.update_idletasks() # 强制GUI更新

    def browse_file(self, entry_widget, title):
        """浏览文件并更新Entry控件"""
        filepath = filedialog.askopenfilename(title=title, filetypes=(("音频文件", "*.wav *.mp3 *.aac *.m4a"), ("所有文件", "*.*")))
        if filepath:
            entry_widget.delete(0, tk.END)
            entry_widget.insert(0, filepath)

    def browse_save_path(self):
        """浏览输出文件保存路径"""
        filepath = filedialog.asksaveasfilename(title="选择输出文件保存位置", defaultextension=".wav", filetypes=(("WAV 文件", "*.wav"), ("MP3 文件", "*.mp3"),("所有文件", "*.*")))
        if filepath:
            self.output_path_entry.delete(0, tk.END)
            self.output_path_entry.insert(0, filepath)

    def add_sfx(self):
        """添加音效文件到列表"""
        filepaths = filedialog.askopenfilenames(title="选择音效文件(可多选)", filetypes=(("音频文件", "*.wav *.mp3 *.aac *.m4a"), ("所有文件", "*.*")))
        for fp in filepaths:
            if fp:
                self.sfx_listbox.insert(tk.END, fp)

    def remove_sfx(self):
        """从列表移除选中的音效"""
        selected_indices = self.sfx_listbox.curselection()
        # 从后往前删除，避免索引变化问题
        for index in reversed(selected_indices):
            self.sfx_listbox.delete(index)

    def validate_inputs(self):
        """校验所有用户输入"""
        if not self.vo_path_entry.get():
            messagebox.showerror("输入错误", "请选择配音 (VO) 文件！")
            return False
        if not self.bgm_path_entry.get():
            messagebox.showerror("输入错误", "请选择背景音乐 (BGM) 文件！")
            return False
        if not self.output_path_entry.get():
            messagebox.showerror("输入错误", "请指定输出文件保存位置！")
            return False

        sfx_files = self.sfx_listbox.get(0, tk.END)
        sfx_times_str = self.sfx_times_text.get("1.0", tk.END).strip().splitlines()
        sfx_times_str = [line for line in sfx_times_str if line.strip()] # 移除空行
        sfx_volume_str = self.sfx_volume_text.get("1.0", tk.END).strip().splitlines()
        sfx_volume_str = [line for line in sfx_volume_str if line.strip()] # 移除空行

        if len(sfx_files) != len(sfx_times_str):
            messagebox.showerror("输入错误", "音效文件数量与音效开始时间数量不匹配！\n请确保每个音效文件都有一行对应的时间。")
            return False

        if sfx_volume_str and len(sfx_files) != len(sfx_volume_str):
            messagebox.showerror("输入错误", "音效文件数量与音量参数数量不匹配！\n请确保每个音效文件都有一行对应的音量参数，或留空使用默认值。")
            return False

        self.sfx_specs = []
        for i, sfx_file in enumerate(sfx_files):
            try:
                spec = {'path': sfx_file, 'start_ms': int(sfx_times_str[i].strip())}
                
                # 处理音量参数
                if i < len(sfx_volume_str):
                    vol_param = sfx_volume_str[i].strip().lower()
                    if vol_param.startswith("peak:"):
                        try:
                            spec['target_peak_dbfs'] = float(vol_param.replace("peak:", "").strip())
                        except ValueError:
                            messagebox.showerror("输入错误", f"音效目标峰值参数 '{vol_param}' 无效，请输入有效的数字。")
                            return False
                    else:
                        try:
                            spec['volume_adjust_db'] = float(vol_param)
                        except ValueError:
                            messagebox.showerror("输入错误", f"音效音量调整参数 '{vol_param}' 无效，请输入有效的数字。")
                            return False
                
                self.sfx_specs.append(spec)
            except ValueError:
                messagebox.showerror("输入错误", f"音效开始时间 '{sfx_times_str[i]}' 无效，请输入纯数字 (毫秒)。")
                return False
        
        try:
            float(self.target_lufs_entry.get())
            float(self.target_tp_entry.get())
            float(self.bgm_ducking_entry.get())
            float(self.vo_peak_norm_entry.get())
        except ValueError:
            messagebox.showerror("输入错误", "LUFS, 真峰值, BGM闪避量, VO峰值标准化参数必须是数字！")
            return False
            
        return True

    def start_processing(self):
        """开始音频处理的主函数"""
        if not self.validate_inputs():
            return

        self.process_button.config(state=tk.DISABLED, text="处理中...")
        self.log_message("开始处理音频...")

        # 从GUI获取参数
        params = {
            'vo_path': self.vo_path_entry.get(),
            'bgm_path': self.bgm_path_entry.get(),
            'sfx_specs': self.sfx_specs, # 已经在validate_inputs中组装好
            'output_path': self.output_path_entry.get(),
            'target_lufs': float(self.target_lufs_entry.get()),
            'target_tp': float(self.target_tp_entry.get()),
            'bgm_ducking_db': float(self.bgm_ducking_entry.get()),
            'vo_peak_norm_dbfs': float(self.vo_peak_norm_entry.get())
        }

        # 在新线程中运行，防止GUI卡死
        thread = threading.Thread(target=self.process_audio_thread, args=(params,))
        thread.daemon = True # 确保主窗口关闭时线程也退出
        thread.start()

    def process_audio_thread(self, params):
        """音频处理的线程函数"""
        try:
            result = self._mix_with_pydub(
                params['vo_path'],
                params['bgm_path'],
                params['sfx_specs'],
                params['bgm_ducking_db'],
                params['vo_peak_norm_dbfs']
            )
            
            if result:
                temp_raw_mix_path, mix_params = result
                self.log_message(f"初步混音完成: {temp_raw_mix_path}")
                self.log_message("开始使用 FFmpeg进行响度标准化...")
                
                # 执行FFmpeg标准化
                success, final_params = self._ffmpeg_loudnorm(
                    temp_raw_mix_path,
                    params['output_path'],
                    params['target_lufs'],
                    params['target_tp']
                )
                
                if success:
                    self.log_message(f"成功！最终文件已保存到: {params['output_path']}")
                    # 更新处理参数显示
                    self.update_params_display(
                        mix_params['vo_params'],
                        mix_params['bgm_params'],
                        mix_params['sfx_params_list'],
                        final_params
                    )
                else:
                    self.log_message("错误：FFmpeg 标准化失败。请检查日志和FFmpeg安装。")
            else:
                self.log_message("错误：初步混音失败。")

        except Exception as e:
            self.log_message(f"处理过程中发生严重错误: {e}")
        finally:
            self.process_button.config(state=tk.NORMAL, text="开始处理音频")
            # 清理临时文件
            temp_raw_mix = os.path.join(os.path.dirname(params['output_path']), "_temp_raw_mix.wav")
            if os.path.exists(temp_raw_mix):
                try:
                    os.remove(temp_raw_mix)
                    self.log_message(f"已清理临时文件: {temp_raw_mix}")
                except Exception as e_del:
                    self.log_message(f"警告：无法删除临时文件 {temp_raw_mix}: {e_del}")

    def update_params_display(self, vo_params, bgm_params, sfx_params_list, final_params):
        """更新处理参数显示"""
        self.params_display_text.delete(1.0, tk.END)
        
        # 显示配音参数
        self.params_display_text.insert(tk.END, "配音 (VO) 处理参数:\n")
        self.params_display_text.insert(tk.END, f"文件: {os.path.basename(vo_params['path'])}\n")
        self.params_display_text.insert(tk.END, f"原始峰值: {vo_params['original_peak']:.2f} dBFS\n")
        self.params_display_text.insert(tk.END, f"原始响度: {vo_params['original_lufs']:.2f} LUFS\n")
        self.params_display_text.insert(tk.END, f"目标峰值: {vo_params['target_peak']:.2f} dBFS\n")
        self.params_display_text.insert(tk.END, f"处理后峰值: {vo_params['final_peak']:.2f} dBFS\n")
        self.params_display_text.insert(tk.END, f"处理后响度: {vo_params['final_lufs']:.2f} LUFS\n\n")

        # 显示背景音乐参数
        self.params_display_text.insert(tk.END, "背景音乐 (BGM) 处理参数:\n")
        self.params_display_text.insert(tk.END, f"文件: {os.path.basename(bgm_params['path'])}\n")
        self.params_display_text.insert(tk.END, f"原始峰值: {bgm_params['original_peak']:.2f} dBFS\n")
        self.params_display_text.insert(tk.END, f"原始响度: {bgm_params['original_lufs']:.2f} LUFS\n")
        self.params_display_text.insert(tk.END, f"闪避量: {bgm_params['ducking_db']:.2f} dB\n")
        self.params_display_text.insert(tk.END, f"处理后峰值: {bgm_params['final_peak']:.2f} dBFS\n")
        self.params_display_text.insert(tk.END, f"处理后响度: {bgm_params['final_lufs']:.2f} LUFS\n\n")

        # 显示音效参数
        if sfx_params_list:
            self.params_display_text.insert(tk.END, "音效 (SFX) 处理参数:\n")
            for i, sfx_params in enumerate(sfx_params_list, 1):
                self.params_display_text.insert(tk.END, f"音效 {i}:\n")
                self.params_display_text.insert(tk.END, f"文件: {os.path.basename(sfx_params['path'])}\n")
                self.params_display_text.insert(tk.END, f"开始时间: {sfx_params['start_ms']} ms\n")
                self.params_display_text.insert(tk.END, f"原始峰值: {sfx_params['original_peak']:.2f} dBFS\n")
                if 'target_peak' in sfx_params:
                    self.params_display_text.insert(tk.END, f"目标峰值: {sfx_params['target_peak']:.2f} dBFS\n")
                if 'volume_adjust' in sfx_params:
                    self.params_display_text.insert(tk.END, f"音量调整: {sfx_params['volume_adjust']:.2f} dB\n")
                self.params_display_text.insert(tk.END, f"处理后峰值: {sfx_params['final_peak']:.2f} dBFS\n\n")

        # 显示最终混音参数
        self.params_display_text.insert(tk.END, "最终混音参数:\n")
        self.params_display_text.insert(tk.END, f"目标响度: {final_params['target_lufs']:.2f} LUFS\n")
        self.params_display_text.insert(tk.END, f"目标真峰值: {final_params['target_tp']:.2f} dBTP\n")
        self.params_display_text.insert(tk.END, f"最终响度: {final_params['final_lufs']:.2f} LUFS\n")
        self.params_display_text.insert(tk.END, f"最终真峰值: {final_params['final_tp']:.2f} dBTP\n")

    def _mix_with_pydub(self, vo_path, bgm_path, sfx_specs, bgm_ducking_db, vo_peak_norm_dbfs):
        """使用pydub进行初步混音"""
        self.log_message("开始初步混音 (pydub)...")
        
        # 用于收集处理参数
        vo_params = {'path': vo_path}
        bgm_params = {'path': bgm_path, 'ducking_db': bgm_ducking_db}
        sfx_params_list = []
        
        try:
            vo = AudioSegment.from_file(vo_path)
            self.log_message(f"配音文件 '{os.path.basename(vo_path)}' 加载成功。时长: {len(vo)/1000:.2f}s")
            
            # 记录VO原始参数
            vo_params['original_peak'] = 20 * np.log10(np.max(np.abs(np.array(vo.get_array_of_samples()))) / 32768.0)
            vo_params['target_peak'] = vo_peak_norm_dbfs
            
            bgm = AudioSegment.from_file(bgm_path)
            self.log_message(f"背景音乐 '{os.path.basename(bgm_path)}' 加载成功。时长: {len(bgm)/1000:.2f}s")
            
            # 记录BGM原始参数
            bgm_params['original_peak'] = 20 * np.log10(np.max(np.abs(np.array(bgm.get_array_of_samples()))) / 32768.0)
            
        except Exception as e:
            self.log_message(f"错误：加载基础音频文件失败: {e}")
            return None, None

        # 1. 配音初步峰值标准化
        self.log_message(f"对配音进行峰值标准化到 {vo_peak_norm_dbfs} dBFS...")
        vo = pydub_peak_normalize(vo, headroom=abs(vo_peak_norm_dbfs))
        vo_params['final_peak'] = 20 * np.log10(np.max(np.abs(np.array(vo.get_array_of_samples()))) / 32768.0)

        # 确定混音总时长
        mix_duration = len(vo)
        self.log_message(f"设定混音总时长为: {mix_duration/1000:.2f}s (基于配音时长)")
        final_mix = AudioSegment.silent(duration=mix_duration, frame_rate=vo.frame_rate)

        # 2. BGM 处理与闪避
        if len(bgm) > mix_duration:
            bgm = bgm[:mix_duration]
            self.log_message("BGM 已裁剪以匹配混音时长。")
        elif len(bgm) < mix_duration:
            loops = (mix_duration // len(bgm)) + 1
            bgm = bgm * loops
            bgm = bgm[:mix_duration]
            self.log_message("BGM 已循环以匹配混音时长。")
        
        if bgm.frame_rate != vo.frame_rate:
            self.log_message(f"警告：BGM采样率({bgm.frame_rate}Hz)与VO采样率({vo.frame_rate}Hz)不一致。将BGM重采样至{vo.frame_rate}Hz。")
            bgm = bgm.set_frame_rate(vo.frame_rate)
        if bgm.channels != vo.channels:
             self.log_message(f"警告：BGM声道数({bgm.channels})与VO声道数({vo.channels})不一致。将BGM转换为{vo.channels}声道。")
             bgm = bgm.set_channels(vo.channels)

        # 应用闪避
        self.log_message(f"对BGM应用闪避，降低 {bgm_ducking_db} dB...")
        bgm_ducked = bgm.apply_gain(bgm_ducking_db)
        bgm_params['final_peak'] = 20 * np.log10(np.max(np.abs(np.array(bgm_ducked.get_array_of_samples()))) / 32768.0)
        final_mix = final_mix.overlay(bgm_ducked)

        # 叠加配音
        final_mix = final_mix.overlay(vo)
        self.log_message("配音已叠加到混音中。")

        # 3. 添加音效
        for sfx_spec in sfx_specs:
            try:
                sfx = AudioSegment.from_file(sfx_spec['path'])
                self.log_message(f"音效 '{os.path.basename(sfx_spec['path'])}' 加载成功。")
                
                # 记录音效参数
                sfx_params = {
                    'path': sfx_spec['path'],
                    'start_ms': sfx_spec['start_ms'],
                    'original_peak': 20 * np.log10(np.max(np.abs(np.array(sfx.get_array_of_samples()))) / 32768.0)
                }
                
                # 音效音量控制
                if 'target_peak_dbfs' in sfx_spec:
                    target_peak = float(sfx_spec['target_peak_dbfs'])
                    self.log_message(f"将音效峰值标准化到 {target_peak} dBFS...")
                    sfx = pydub_peak_normalize(sfx, headroom=abs(target_peak))
                    sfx_params['target_peak'] = target_peak
                elif 'volume_adjust_db' in sfx_spec:
                    adjust_db = float(sfx_spec['volume_adjust_db'])
                    self.log_message(f"对音效应用音量调整 {adjust_db} dB...")
                    sfx = sfx.apply_gain(adjust_db)
                    sfx_params['volume_adjust'] = adjust_db
                else:
                    self.log_message(f"对音效进行默认峰值标准化 ({DEFAULT_SFX_PEAK_NORM_DBFS} dBFS)...")
                    sfx = pydub_peak_normalize(sfx, headroom=abs(DEFAULT_SFX_PEAK_NORM_DBFS))
                    sfx_params['target_peak'] = DEFAULT_SFX_PEAK_NORM_DBFS
                
                # 记录处理后的峰值
                sfx_params['final_peak'] = 20 * np.log10(np.max(np.abs(np.array(sfx.get_array_of_samples()))) / 32768.0)
                sfx_params_list.append(sfx_params)
                
                if sfx.frame_rate != final_mix.frame_rate:
                    sfx = sfx.set_frame_rate(final_mix.frame_rate)
                if sfx.channels != final_mix.channels:
                    sfx = sfx.set_channels(final_mix.channels)

                start_ms = sfx_spec['start_ms']
                self.log_message(f"在 {start_ms}ms 处叠加音效 '{os.path.basename(sfx_spec['path'])}'...")
                final_mix = final_mix.overlay(sfx, position=start_ms)
            except Exception as e:
                self.log_message(f"错误：处理或叠加音效 '{sfx_spec['path']}' 失败: {e}")
        
        # 保存初步混音结果到临时文件
        output_dir = os.path.dirname(self.output_path_entry.get())
        if not output_dir:
            output_dir = "." 
        temp_raw_mix_path = os.path.join(output_dir, "_temp_raw_mix.wav")

        try:
            final_mix.export(temp_raw_mix_path, format="wav")
            self.log_message(f"初步混音结果已保存到临时文件: {temp_raw_mix_path}")
            
            # 返回处理参数
            return temp_raw_mix_path, {
                'vo_params': vo_params,
                'bgm_params': bgm_params,
                'sfx_params_list': sfx_params_list
            }
        except Exception as e:
            self.log_message(f"错误：导出初步混音文件失败: {e}")
            return None, None

    def _ffmpeg_loudnorm(self, input_path, output_path, target_i, target_tp, target_lra=7.0):
        """使用FFmpeg的loudnorm滤波器进行标准化"""
        ffmpeg_executable = "ffmpeg"

        cmd = [
            ffmpeg_executable,
            '-i', input_path,
            '-af', f'loudnorm=I={target_i}:TP={target_tp}:LRA={target_lra}:print_format=json',
            '-ar', '48000',
            '-ac', '2',
            '-y',
            output_path
        ]
        self.log_message(f"执行 FFmpeg 命令: {' '.join(cmd)}")

        try:
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8', errors='replace')
            stdout, stderr = process.communicate(timeout=120)

            if process.returncode == 0:
                self.log_message("FFmpeg 标准化成功完成。")
                self.log_message("FFmpeg 输出信息:")
                
                # 解析FFmpeg输出的JSON数据
                json_stats_str = ""
                for line in stderr.splitlines():
                    if line.strip().startswith("{") and line.strip().endswith("}"):
                        json_stats_str = line.strip()
                        break
                
                final_params = {
                    'target_lufs': target_i,
                    'target_tp': target_tp,
                    'final_lufs': 0.0,
                    'final_tp': 0.0
                }
                
                if json_stats_str:
                    try:
                        stats = json.loads(json_stats_str)
                        self.log_message("响度统计 (由ffmpeg测量):")
                        for key, value in stats.items():
                            self.log_message(f"  {key}: {value}")
                            if key == 'input_i':
                                final_params['final_lufs'] = float(value)
                            elif key == 'input_tp':
                                final_params['final_tp'] = float(value)
                    except json.JSONDecodeError:
                        self.log_message("未能解析ffmpeg的JSON响度统计。")
                else:
                    self.log_message("在ffmpeg输出中未找到JSON响度统计。")
                
                if stdout: self.log_message(f"FFmpeg stdout:\n{stdout}")
                non_json_stderr = "\n".join([line for line in stderr.splitlines() if not (line.strip().startswith("{") and line.strip().endswith("}"))])
                if non_json_stderr.strip():
                    self.log_message(f"FFmpeg stderr (除JSON外):\n{non_json_stderr}")

                return True, final_params
            else:
                self.log_message(f"FFmpeg 执行错误 (返回码: {process.returncode})")
                self.log_message(f"FFmpeg stdout:\n{stdout}")
                self.log_message(f"FFmpeg stderr:\n{stderr}")
                return False, None
        except FileNotFoundError:
            self.log_message("错误: FFmpeg 未找到。请确保已正确安装FFmpeg并将其添加至系统PATH环境变量。")
            messagebox.showerror("FFmpeg错误", "FFmpeg 未找到。\n请确保已正确安装FFmpeg并将其添加至系统PATH环境变量。")
            return False, None
        except subprocess.TimeoutExpired:
            self.log_message("错误: FFmpeg 执行超时。音频文件可能过大或处理复杂。")
            messagebox.showerror("FFmpeg错误", "FFmpeg 执行超时。")
            if process: process.kill()
            return False, None
        except Exception as e:
            self.log_message(f"执行 FFmpeg 时发生未知错误: {e}")
            return False, None

if __name__ == "__main__":
    main_root = tk.Tk()
    app = AudioProcessorApp(main_root)
    main_root.mainloop()