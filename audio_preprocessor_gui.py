import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import librosa
import numpy as np
import sounddevice as sd
import soundfile as sf
import os
import threading
import time
from scipy import signal

# --- 配置 ---
TARGET_SR = 48000  # 目标采样率

# 检查音频设备
def check_audio_devices():
    try:
        devices = sd.query_devices()
        if not devices:
            raise RuntimeError("未找到音频设备")
        return True
    except Exception as e:
        messagebox.showerror("错误", f"音频设备检查失败: {str(e)}")
        return False

# --- 音频处理函数 ---
def peak_normalize_audio(audio_data, target_dbfs=-0.1):
    """
    对音频数据进行峰值归一化
    """
    # 确保数据是浮点数
    if np.issubdtype(audio_data.dtype, np.integer):
        if audio_data.dtype == np.int16:
            audio_float = audio_data.astype(np.float32) / 32768.0
        elif audio_data.dtype == np.int32:
            audio_float = audio_data.astype(np.float32) / (2**31)
        elif audio_data.dtype == np.uint8:
            audio_float = (audio_data.astype(np.float32) - 128.0) / 128.0
        else:
            raise ValueError(f"不支持的整数类型: {audio_data.dtype}")
    else:
        audio_float = audio_data.astype(np.float32)

    # 找到当前峰值
    current_peak = np.max(np.abs(audio_float))
    
    # 处理静音情况
    if current_peak < 1e-9:
        return audio_data

    # 计算目标峰值（线性）
    target_peak = 10**(target_dbfs / 20.0)
    
    # 计算增益
    gain = target_peak / current_peak
    
    # 应用增益
    normalized_audio = audio_float * gain
    
    # 防止削波
    normalized_audio = np.clip(normalized_audio, -1.0, 1.0)
    
    # 转换回原始类型
    if np.issubdtype(audio_data.dtype, np.integer):
        if audio_data.dtype == np.int16:
            return np.round(normalized_audio * 32767.0).astype(np.int16)
        elif audio_data.dtype == np.int32:
            return np.round(normalized_audio * (2**31 - 1)).astype(np.int32)
        elif audio_data.dtype == np.uint8:
            return np.round((normalized_audio * 127.0) + 128.0).astype(np.uint8)
    
    return normalized_audio.astype(audio_data.dtype)

def preprocess_audio(y, sr, target_sr=TARGET_SR, normalize_type=None, target_dbfs=-0.1):
    """
    应用预处理步骤：单声道转换、重采样、归一化
    """
    # 1. 转换为单声道（如果是立体声）
    if y.ndim > 1 and y.shape[0] > 1:
        y_mono = librosa.to_mono(y)
    else:
        y_mono = y.flatten()

    # 2. 重采样
    if sr != target_sr:
        y_resampled = librosa.resample(y_mono, orig_sr=sr, target_sr=target_sr)
    else:
        y_resampled = y_mono
    
    # 3. 归一化
    if normalize_type == "peak":
        y_normalized = peak_normalize_audio(y_resampled, target_dbfs)
    elif normalize_type == "preprocess":
        # 预处理归一化也使用dBFS归一化
        y_normalized = peak_normalize_audio(y_resampled, target_dbfs)
    else:
        y_normalized = y_resampled
    
    return y_normalized, target_sr

def get_audio_info(y, sr, description="音频"):
    """
    收集音频信号的基本信息
    """
    info = f"--- {description} 参数 ---\n"
    info += f"采样率: {sr} Hz\n"
    
    if y.ndim > 1:
        info += f"声道数: {y.shape[0]}\n"
        duration = y.shape[1] / sr
        max_amp = np.max(np.abs(y))
        min_amp = np.min(y)
    else:
        info += f"声道数: 1\n"
        duration = len(y) / sr
        max_amp = np.max(np.abs(y))
        min_amp = np.min(y)

    # 计算 dBFS
    dbfs = 20 * np.log10(max_amp) if max_amp > 0 else -np.inf
    
    # 计算 LUFS (使用简化的计算方法)
    # 注意：这是一个简化的实现，实际的 LUFS 计算更复杂
    window_size = int(0.4 * sr)  # 400ms 窗口
    rms = np.sqrt(np.mean(y**2))
    lufs = 20 * np.log10(rms) if rms > 0 else -np.inf
    
    # 计算动态范围
    dynamic_range = 20 * np.log10(max_amp / (np.mean(np.abs(y)) + 1e-10))

    info += f"时长: {duration:.2f} 秒\n"
    info += f"最大振幅: {max_amp:.4f}\n"
    info += f"最小振幅: {min_amp:.4f}\n"
    info += f"dBFS: {dbfs:.2f} dB\n"
    info += f"LUFS: {lufs:.2f} dB\n"
    info += f"动态范围: {dynamic_range:.2f} dB\n"
    
    return info

# --- GUI 应用程序类 ---
class AudioPreprocessorApp:
    def __init__(self, master):
        self.master = master
        master.title("音频预处理器")
        master.geometry("800x600")

        # 检查音频设备
        if not check_audio_devices():
            messagebox.showwarning("警告", "未检测到可用的音频设备，播放功能可能无法使用")

        self.filepath = None
        self.y_orig = None
        self.sr_orig = None
        self.y_processed = None
        self.sr_processed = None
        self.playback_thread = None
        self.is_playing = False
        self.current_position = 0
        self.playback_start_time = 0
        self.paused_position = 0
        self.paused_time = 0

        # --- UI 元素 ---
        # 文件选择
        self.file_frame = ttk.LabelFrame(master, text="1. 加载音频")
        self.file_frame.pack(padx=10, pady=5, fill="x")

        self.select_button = ttk.Button(self.file_frame, text="选择音频文件 (.wav, .mp3)", command=self.select_file)
        self.select_button.pack(pady=5)
        self.file_label = ttk.Label(self.file_frame, text="未选择文件")
        self.file_label.pack(pady=5)

        # 原始音频信息与播放控制
        self.orig_frame = ttk.LabelFrame(master, text="原始音频")
        self.orig_frame.pack(padx=10, pady=5, fill="x")
        self.orig_info_text = tk.Text(self.orig_frame, height=8, width=80, wrap=tk.WORD, state=tk.DISABLED)
        self.orig_info_text.pack(pady=5, padx=5)
        
        # 原始音频进度条
        self.orig_progress_frame = ttk.Frame(self.orig_frame)
        self.orig_progress_frame.pack(fill="x", padx=5, pady=5)
        self.orig_progress = ttk.Progressbar(self.orig_progress_frame, mode='determinate', length=300)
        self.orig_progress.pack(side=tk.LEFT, padx=5)
        self.orig_time_label = ttk.Label(self.orig_progress_frame, text="00:00 / 00:00")
        self.orig_time_label.pack(side=tk.LEFT, padx=5)
        
        button_orig_frame = ttk.Frame(self.orig_frame)
        button_orig_frame.pack(fill="x")
        self.play_orig_button = ttk.Button(button_orig_frame, text="播放原始音频", 
                                         command=lambda: self.play_audio_data(self.y_orig, self.sr_orig, "orig"))
        self.play_orig_button.pack(side=tk.LEFT, padx=5, pady=5)
        self.pause_orig_button = ttk.Button(button_orig_frame, text="暂停", 
                                          command=lambda: self.pause_audio("orig"), state=tk.DISABLED)
        self.pause_orig_button.pack(side=tk.LEFT, padx=5, pady=5)

        # 处理控制
        self.process_frame = ttk.LabelFrame(master, text="2. 处理音频")
        self.process_frame.pack(padx=10, pady=5, fill="x")
        
        resample_label = ttk.Label(self.process_frame, text=f"目标采样率: {TARGET_SR} Hz")
        resample_label.pack(pady=2)

        # 添加归一化选项
        normalize_frame = ttk.Frame(self.process_frame)
        normalize_frame.pack(pady=5)
        
        ttk.Label(normalize_frame, text="归一化类型:").pack(side=tk.LEFT, padx=5)
        self.normalize_type = tk.StringVar(value="none")
        ttk.Radiobutton(normalize_frame, text="无", variable=self.normalize_type, 
                       value="none").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(normalize_frame, text="预处理归一化", variable=self.normalize_type, 
                       value="preprocess").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(normalize_frame, text="峰值归一化", variable=self.normalize_type, 
                       value="peak").pack(side=tk.LEFT, padx=5)

        # 添加目标dBFS输入
        dbfs_frame = ttk.Frame(self.process_frame)
        dbfs_frame.pack(pady=5)
        ttk.Label(dbfs_frame, text="目标dBFS:").pack(side=tk.LEFT, padx=5)
        self.target_dbfs = tk.StringVar(value="-0.1")
        ttk.Entry(dbfs_frame, textvariable=self.target_dbfs, width=10).pack(side=tk.LEFT, padx=5)

        self.process_button = ttk.Button(self.process_frame, text="处理音频", 
                                       command=self.process_audio_file, state=tk.DISABLED)
        self.process_button.pack(pady=5)

        # 处理后音频信息与播放控制
        self.proc_frame = ttk.LabelFrame(master, text="处理后音频")
        self.proc_frame.pack(padx=10, pady=5, fill="x")
        self.proc_info_text = tk.Text(self.proc_frame, height=8, width=80, wrap=tk.WORD, state=tk.DISABLED)
        self.proc_info_text.pack(pady=5, padx=5)
        
        # 处理后音频进度条
        self.proc_progress_frame = ttk.Frame(self.proc_frame)
        self.proc_progress_frame.pack(fill="x", padx=5, pady=5)
        self.proc_progress = ttk.Progressbar(self.proc_progress_frame, mode='determinate', length=300)
        self.proc_progress.pack(side=tk.LEFT, padx=5)
        self.proc_time_label = ttk.Label(self.proc_progress_frame, text="00:00 / 00:00")
        self.proc_time_label.pack(side=tk.LEFT, padx=5)
        
        button_proc_frame = ttk.Frame(self.proc_frame)
        button_proc_frame.pack(fill="x")
        self.play_proc_button = ttk.Button(button_proc_frame, text="播放处理后音频", 
                                         command=lambda: self.play_audio_data(self.y_processed, self.sr_processed, "proc"))
        self.play_proc_button.pack(side=tk.LEFT, padx=5, pady=5)
        self.pause_proc_button = ttk.Button(button_proc_frame, text="暂停", 
                                          command=lambda: self.pause_audio("proc"), state=tk.DISABLED)
        self.pause_proc_button.pack(side=tk.LEFT, padx=5, pady=5)
        self.save_proc_button = ttk.Button(button_proc_frame, text="保存处理后音频 (.wav)", 
                                         command=self.save_processed_audio, state=tk.DISABLED)
        self.save_proc_button.pack(side=tk.LEFT, padx=5, pady=5)

        # 停止播放按钮
        self.stop_button = ttk.Button(master, text="停止播放", command=self.stop_audio, state=tk.DISABLED)
        self.stop_button.pack(pady=10)
        
        self.status_label = ttk.Label(master, text="状态: 就绪")
        self.status_label.pack(side=tk.BOTTOM, fill="x", padx=10, pady=5)

    def _update_text_widget(self, widget, text):
        widget.config(state=tk.NORMAL)
        widget.delete(1.0, tk.END)
        widget.insert(tk.END, text)
        widget.config(state=tk.DISABLED)

    def _format_time(self, seconds):
        """将秒数转换为 MM:SS 格式"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"

    def _update_progress(self, audio_type):
        """更新进度条和时间标签"""
        if not self.is_playing:
            return

        current_time = time.time() - self.playback_start_time
        total_duration = len(self.y_orig if audio_type == "orig" else self.y_processed) / \
                        (self.sr_orig if audio_type == "orig" else self.sr_processed)
        
        if current_time >= total_duration:
            self.stop_audio()
            return

        progress = (current_time / total_duration) * 100
        progress_bar = self.orig_progress if audio_type == "orig" else self.proc_progress
        time_label = self.orig_time_label if audio_type == "orig" else self.proc_time_label
        
        progress_bar["value"] = progress
        time_label.config(text=f"{self._format_time(current_time)} / {self._format_time(total_duration)}")
        
        self.master.after(100, lambda: self._update_progress(audio_type))

    def select_file(self):
        filepath = filedialog.askopenfilename(
            title="选择音频文件",
            filetypes=(("音频文件", "*.wav *.mp3 *.flac *.aac"), ("所有文件", "*.*"))
        )
        if filepath:
            self.filepath = filepath
            self.file_label.config(text=os.path.basename(filepath))
            self.status_label.config(text="状态: 正在加载原始音频...")
            self.master.update_idletasks()
            try:
                self.y_orig, self.sr_orig = librosa.load(self.filepath, sr=None, mono=False)
                
                info_orig = get_audio_info(self.y_orig, self.sr_orig, "原始音频")
                self._update_text_widget(self.orig_info_text, info_orig)
                
                self.play_orig_button.config(state=tk.NORMAL)
                self.process_button.config(state=tk.NORMAL)
                self.play_proc_button.config(state=tk.DISABLED)
                self.save_proc_button.config(state=tk.DISABLED)
                self._update_text_widget(self.proc_info_text, "处理后音频信息将显示在这里")
                self.status_label.config(text="状态: 原始音频已加载")
            except Exception as e:
                messagebox.showerror("错误", f"加载音频文件失败: {e}")
                self.status_label.config(text="状态: 加载文件出错")
                self.filepath = None
                self.file_label.config(text="未选择文件")
                self.play_orig_button.config(state=tk.DISABLED)
                self.process_button.config(state=tk.DISABLED)

    def process_audio_file(self):
        if self.y_orig is None or self.sr_orig is None:
            messagebox.showwarning("警告", "请先加载音频文件")
            return

        try:
            target_dbfs = float(self.target_dbfs.get())
        except ValueError:
            messagebox.showerror("错误", "目标dBFS必须是有效的数字")
            return

        self.status_label.config(text="状态: 正在处理音频...")
        self.master.update_idletasks()
        try:
            normalize_type = self.normalize_type.get()
            self.y_processed, self.sr_processed = preprocess_audio(
                self.y_orig, 
                self.sr_orig, 
                target_sr=TARGET_SR,
                normalize_type=normalize_type,
                target_dbfs=target_dbfs
            )
            info_proc = get_audio_info(self.y_processed, self.sr_processed, "处理后音频")
            self._update_text_widget(self.proc_info_text, info_proc)
            self.play_proc_button.config(state=tk.NORMAL)
            self.save_proc_button.config(state=tk.NORMAL)
            self.status_label.config(text="状态: 音频处理完成")
        except Exception as e:
            messagebox.showerror("错误", f"处理音频失败: {str(e)}")
            self.status_label.config(text="状态: 处理过程出错")

    def _play_audio_task(self, data, samplerate, audio_type):
        try:
            self.is_playing = True
            self.playback_start_time = time.time() - self.paused_time
            self._update_progress(audio_type)
            
            try:
                sd.play(data, samplerate)
                sd.wait()
            except Exception as e:
                raise RuntimeError(f"音频播放失败: {str(e)}")
            
            self.is_playing = False
            self.master.after(0, lambda: self.stop_audio())
        except Exception as e:
            self.master.after(0, lambda: messagebox.showerror("播放错误", f"无法播放音频: {str(e)}"))
            self.is_playing = False
        finally:
            self.master.after(0, lambda: self.stop_button.config(state=tk.DISABLED))

    def play_audio_data(self, data, samplerate, audio_type):
        if data is None or samplerate is None:
            messagebox.showwarning("播放信息", "没有可播放的音频数据")
            return

        self.stop_audio()

        if data.ndim > 1:
            play_data = data.T 
        else:
            play_data = data

        self.status_label.config(text=f"状态: 正在播放音频 ({samplerate} Hz)...")
        self.stop_button.config(state=tk.NORMAL)
        
        if audio_type == "orig":
            self.pause_orig_button.config(state=tk.NORMAL)
            self.play_orig_button.config(state=tk.DISABLED)
        else:
            self.pause_proc_button.config(state=tk.NORMAL)
            self.play_proc_button.config(state=tk.DISABLED)
        
        self.playback_thread = threading.Thread(target=self._play_audio_task, 
                                             args=(play_data, samplerate, audio_type))
        self.playback_thread.daemon = True
        self.playback_thread.start()

    def pause_audio(self, audio_type):
        if self.is_playing:
            try:
                sd.stop()
                self.paused_time = time.time() - self.playback_start_time
                self.is_playing = False
                if audio_type == "orig":
                    self.pause_orig_button.config(text="继续", command=lambda: self.resume_audio("orig"))
                else:
                    self.pause_proc_button.config(text="继续", command=lambda: self.resume_audio("proc"))
                self.status_label.config(text="状态: 播放已暂停")
            except Exception as e:
                messagebox.showerror("错误", f"暂停音频失败: {str(e)}")

    def resume_audio(self, audio_type):
        if not self.is_playing:
            try:
                data = self.y_orig if audio_type == "orig" else self.y_processed
                samplerate = self.sr_orig if audio_type == "orig" else self.sr_processed
                if data.ndim > 1:
                    play_data = data.T
                else:
                    play_data = data
                    
                self.play_audio_data(play_data, samplerate, audio_type)
                if audio_type == "orig":
                    self.pause_orig_button.config(text="暂停", command=lambda: self.pause_audio("orig"))
                else:
                    self.pause_proc_button.config(text="暂停", command=lambda: self.pause_audio("proc"))
            except Exception as e:
                messagebox.showerror("错误", f"继续播放失败: {str(e)}")

    def stop_audio(self):
        try:
            sd.stop()
            self.is_playing = False
            self.paused_time = 0
            if self.playback_thread and self.playback_thread.is_alive():
                pass
            self.stop_button.config(state=tk.DISABLED)
            self.pause_orig_button.config(state=tk.DISABLED, text="暂停")
            self.pause_proc_button.config(state=tk.DISABLED, text="暂停")
            self.play_orig_button.config(state=tk.NORMAL)
            self.play_proc_button.config(state=tk.NORMAL)
            self.orig_progress["value"] = 0
            self.proc_progress["value"] = 0
            self.orig_time_label.config(text="00:00 / 00:00")
            self.proc_time_label.config(text="00:00 / 00:00")
            self.status_label.config(text="状态: 播放已停止")
        except Exception as e:
            messagebox.showerror("错误", f"停止播放失败: {str(e)}")

    def save_processed_audio(self):
        if self.y_processed is None or self.sr_processed is None:
            messagebox.showwarning("警告", "没有可保存的处理后音频")
            return

        save_path = filedialog.asksaveasfilename(
            defaultextension=".wav",
            filetypes=(("WAV文件", "*.wav"), ("所有文件", "*.*")),
            title="保存处理后音频"
        )
        if save_path:
            self.status_label.config(text="状态: 正在保存处理后音频...")
            self.master.update_idletasks()
            try:
                sf.write(save_path, self.y_processed, self.sr_processed, subtype='PCM_16')
                messagebox.showinfo("成功", f"处理后音频已保存至:\n{save_path}")
                self.status_label.config(text="状态: 处理后音频已保存")
            except Exception as e:
                messagebox.showerror("错误", f"保存音频失败: {e}")
                self.status_label.config(text="状态: 保存音频出错")

# --- 主程序 ---
if __name__ == "__main__":
    root = tk.Tk()
    app = AudioPreprocessorApp(root)
    root.mainloop()