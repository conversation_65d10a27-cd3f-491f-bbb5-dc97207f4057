import subprocess
import json
import shlex

def analyze_loudnorm(input_file):
    """运行 FFmpeg loudnorm 分析步骤"""
    cmd = [
        'ffmpeg',
        '-i', input_file,
        '-af', 'loudnorm=I=-16:TP=-0.1:LRA=11:print_format=json',
        '-f', 'null', '-'
    ]
    process = subprocess.Popen(cmd, stderr=subprocess.PIPE, stdout=subprocess.PIPE)
    _, stderr = process.communicate()
    stderr_text = stderr.decode()

    # 从 stderr 中提取 JSON（FFmpeg 把结果打印到 stderr）
    json_start = stderr_text.find('{')
    json_end = stderr_text.rfind('}') + 1
    json_str = stderr_text[json_start:json_end]
    loudnorm_data = json.loads(json_str)
    return loudnorm_data

def apply_loudnorm(input_file, output_file, loudnorm_data):
    """使用测量值执行第二次 loudnorm 归一化处理"""
    filter_str = (
        f"loudnorm=I=-16:TP=-0.1:LRA=11:measured_I={loudnorm_data['input_i']}:"
        f"measured_TP={loudnorm_data['input_tp']}:measured_LRA={loudnorm_data['input_lra']}:"
        f"measured_thresh={loudnorm_data['input_thresh']}:offset={loudnorm_data['target_offset']}:"
        "linear=true:print_format=summary"
    )

    cmd = [
        'ffmpeg',
        '-i', input_file,
        '-af', filter_str,
        '-ar', '44100',  # 保持采样率一致
        '-y',  # 自动覆盖输出文件
        output_file
    ]
    subprocess.run(cmd)

def normalize_mp3(input_path, output_path):
    print("🔍 正在分析 loudnorm 参数...")
    loudnorm_info = analyze_loudnorm(input_path)
    print("✅ 分析完成，正在应用归一化...")
    apply_loudnorm(input_path, output_path, loudnorm_info)
    print("🎉 归一化完成，输出文件:", output_path)


# 示例用法
if __name__ == "__main__":
    input_mp3 = "/Users/<USER>/Projects/yk/java/video_ai_yk/video_ai_yk_server/.tmp/463025413271133434/1/bgm.mp3"
    output_mp3 = "/Users/<USER>/Downloads/normalized.mp3"
    normalize_mp3(input_mp3, output_mp3)
