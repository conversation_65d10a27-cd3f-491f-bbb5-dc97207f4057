name: linter
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    if: ${{ github.repository == 'facebookresearch/demucs' || github.event_name == 'workflow_dispatch' }}
    steps:
    - uses: actions/checkout@v2
    - uses: actions/setup-python@v2
      with:
        python-version: 3.8

    - uses: actions/cache@v2
      with:
        path: env
        key: env-${{ hashFiles('**/requirements.txt', '.github/workflows/*') }}

    - name: Install dependencies
      run: |
        python3 -m venv env
        . env/bin/activate
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install '.[dev]'


    - name: Run linter
      run: |
        . env/bin/activate
        make linter
