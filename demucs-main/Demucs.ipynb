{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "Be9yoh-ILfRr"}, "source": ["# Hybrid Demucs\n", "\n", "Feel free to use the Colab version:\n", "https://colab.research.google.com/drive/1dC9nVxk3V_VPjUADsnFu8EiT-xnU1tGH?usp=sharing"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 139}, "colab_type": "code", "executionInfo": {"elapsed": 12277, "status": "ok", "timestamp": 1583778134659, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "https://lh3.googleusercontent.com/a-/AOh14GgLl2RbW64ZyWz3Y8IBku0zhHCMnt7fz7fEl0LTdA=s64", "userId": "14811735256675200480"}, "user_tz": 180}, "id": "kOjIPLlzhPfn", "outputId": "c75f17ec-b576-4105-bc5b-c2ac9c1018a3"}, "outputs": [], "source": ["!pip install -U demucs\n", "# or for local development, if you have a clone of <PERSON><PERSON><PERSON>\n", "# pip install -e ."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "5lYOzKKCKAbJ"}, "outputs": [], "source": ["# You can use the `demucs` command line to separate tracks\n", "!demucs test.mp3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# You can also load directly the pretrained models,\n", "# for instance for the MDX 2021 winning model of Track A:\n", "from demucs import pretrained\n", "model = pretrained.get_model('mdx')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Because `model` is a bag of 4 models, you cannot directly call it on your data,\n", "# but the `apply_model` will know what to do of it.\n", "import torch\n", "from demucs.apply import apply_model\n", "x = torch.randn(1, 2, 44100 * 10)  # ten seconds of white noise for the demo\n", "out = apply_model(model, x)[0]     # shape is [S, C, T] with S the number of sources\n", "\n", "# So let see, where is all the white noise content is going ?\n", "for name, source in zip(model.sources, out):\n", "    print(name, source.std() / x.std())\n", "# The outputs are quite weird to be fair, not what I would have expected."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# now let's take a single model from the bag, and let's test it on a pure cosine\n", "freq = 440  # in Hz\n", "sr = model.samplerate\n", "t = torch.arange(10 * sr).float() / sr\n", "x = torch.cos(2 * 3.1416 * freq * t).expand(1, 2, -1)\n", "sub_model = model.models[3]\n", "out = sub_model(x)[0]\n", "\n", "# Same question where does it go?\n", "for name, source in zip(model.sources, out):\n", "    print(name, source.std() / x.std())\n", "    \n", "# Well now it makes much more sense, all the energy is going\n", "# in the `other` source.\n", "# Feel free to try lower pitch (try 80 Hz) to see what happens !"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# For training or more fun, refer to the Demucs README on our repo\n", "# https://github.com/facebookresearch/demucs/tree/main/demucs"]}], "metadata": {"accelerator": "GPU", "colab": {"authorship_tag": "ABX9TyM9xpVr1M86NRcjtQ7g9tCx", "collapsed_sections": [], "name": "Demucs.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 1}