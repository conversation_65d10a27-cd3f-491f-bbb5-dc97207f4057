from flask import Flask, render_template, request, jsonify, send_file
import os
from werkzeug.utils import secure_filename
import demucs.separate
import tempfile
import shutil

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['ALLOWED_EXTENSIONS'] = {'mp3', 'wav', 'flac', 'ogg'}

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({'error': '没有文件被上传'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '没有选择文件'}), 400
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # 创建临时目录用于存储分离后的音频
        with tempfile.TemporaryDirectory() as temp_dir:
            # 使用 demucs 分离音频
            # demucs.separate.main([filepath, '-o', temp_dir, '--two-stems=vocals'])
            demucs.separate.main([filepath, '-o', temp_dir, '-n', 'htdemucs_6s'])
            
            # 获取分离后的文件路径
            # separated_dir = os.path.join(temp_dir, 'htdemucs', os.path.splitext(filename)[0])
            separated_dir = os.path.join(temp_dir, 'htdemucs_6s', os.path.splitext(filename)[0])
            
            # 获取所有分离出的音轨文件路径
            drums_path = os.path.join(separated_dir, 'drums.wav')
            bass_path = os.path.join(separated_dir, 'bass.wav')
            vocals_path = os.path.join(separated_dir, 'vocals.wav')
            guitar_path = os.path.join(separated_dir, 'guitar.wav')
            piano_path = os.path.join(separated_dir, 'piano.wav')
            other_path = os.path.join(separated_dir, 'other.wav')
            
            # 将分离后的文件复制到静态文件目录
            static_dir = os.path.join('web_ui', 'static', 'separated')
            os.makedirs(static_dir, exist_ok=True)
            
            drums_static = os.path.join(static_dir, f'{os.path.splitext(filename)[0]}_drums.wav')
            bass_static = os.path.join(static_dir, f'{os.path.splitext(filename)[0]}_bass.wav')
            vocals_static = os.path.join(static_dir, f'{os.path.splitext(filename)[0]}_vocals.wav')
            guitar_static = os.path.join(static_dir, f'{os.path.splitext(filename)[0]}_guitar.wav')
            piano_static = os.path.join(static_dir, f'{os.path.splitext(filename)[0]}_piano.wav')
            other_static = os.path.join(static_dir, f'{os.path.splitext(filename)[0]}_other.wav')
            
            shutil.copy2(drums_path, drums_static)
            shutil.copy2(bass_path, bass_static)
            shutil.copy2(vocals_path, vocals_static)
            shutil.copy2(guitar_path, guitar_static)
            shutil.copy2(piano_path, piano_static)
            shutil.copy2(other_path, other_static)
            
            return jsonify({
                'success': True,
                'drums': f'/static/separated/{os.path.basename(drums_static)}',
                'bass': f'/static/separated/{os.path.basename(bass_static)}',
                'vocals': f'/static/separated/{os.path.basename(vocals_static)}',
                'guitar': f'/static/separated/{os.path.basename(guitar_static)}',
                'piano': f'/static/separated/{os.path.basename(piano_static)}',
                'other': f'/static/separated/{os.path.basename(other_static)}'
            })
    
    return jsonify({'error': '不支持的文件类型'}), 400

if __name__ == '__main__':
    app.run(debug=True, port=5001) 