<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐分离工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .audio-container {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .audio-player {
            width: 100%;
            margin-bottom: 10px;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .upload-container {
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .upload-container:hover {
            border-color: #0d6efd;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">音乐分离工具</h1>
        
        <div class="upload-container">
            <form id="uploadForm" enctype="multipart/form-data">
                <input type="file" class="form-control" id="audioFile" accept=".mp3,.wav,.flac,.ogg" required>
                <button type="submit" class="btn btn-primary mt-3">上传并分离</button>
            </form>
        </div>

        <div class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在处理音频，请稍候...</p>
        </div>

        <div id="resultContainer" class="audio-container" style="display: none;">
            <h3>分离结果</h3>
            
            <div class="mb-4">
                <h4>人声</h4>
                <audio id="vocalsPlayer" class="audio-player" controls>
                    <source src="" type="audio/wav">
                    您的浏览器不支持音频播放
                </audio>
            </div>

            <div class="mb-4">
                <h4>鼓</h4>
                <audio id="drumsPlayer" class="audio-player" controls>
                    <source src="" type="audio/wav">
                    您的浏览器不支持音频播放
                </audio>
            </div>

            <div class="mb-4">
                <h4>贝司</h4>
                <audio id="bassPlayer" class="audio-player" controls>
                    <source src="" type="audio/wav">
                    您的浏览器不支持音频播放
                </audio>
            </div>
            
            <div class="mb-4">
                <h4>吉他</h4>
                <audio id="guitarPlayer" class="audio-player" controls>
                    <source src="" type="audio/wav">
                    您的浏览器不支持音频播放
                </audio>
            </div>

            <div class="mb-4">
                <h4>钢琴</h4>
                <audio id="pianoPlayer" class="audio-player" controls>
                    <source src="" type="audio/wav">
                    您的浏览器不支持音频播放
                </audio>
            </div>

            <div class="mb-4">
                <h4>其他</h4>
                <audio id="otherPlayer" class="audio-player" controls>
                    <source src="" type="audio/wav">
                    您的浏览器不支持音频播放
                </audio>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const fileInput = document.getElementById('audioFile');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('请选择音频文件');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            // 显示加载动画
            document.querySelector('.loading').style.display = 'block';
            document.getElementById('resultContainer').style.display = 'none';

            try {
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    // 更新音频播放器源
                    document.getElementById('vocalsPlayer').src = data.vocals;
                    document.getElementById('drumsPlayer').src = data.drums;
                    document.getElementById('bassPlayer').src = data.bass;
                    document.getElementById('guitarPlayer').src = data.guitar;
                    document.getElementById('pianoPlayer').src = data.piano;
                    document.getElementById('otherPlayer').src = data.other;
                    
                    // 显示结果容器
                    document.getElementById('resultContainer').style.display = 'block';
                } else {
                    alert(data.error || '处理失败');
                }
            } catch (error) {
                alert('上传失败：' + error.message);
            } finally {
                // 隐藏加载动画
                document.querySelector('.loading').style.display = 'none';
            }
        });
    </script>
</body>
</html> 