import customtkinter as ctk
from tkinter import filedialog, messagebox
import librosa
import numpy as np
import soundfile as sf
import sounddevice as sd
import threading
import os
from datetime import datetime

# --- 音频分析核心逻辑 ---
def find_best_bgm_segments(audio_path, target_duration_sec=20, energy_threshold_ratio=0.2, stability_preference=0.7, num_segments=3):
    """
    分析BGM，尝试找到多个"最佳"的片段。

    参数:
    - audio_path (str): BGM文件路径。
    - target_duration_sec (float): 期望的片段时长 (秒)。
    - energy_threshold_ratio (float): 能量阈值比例，用于去除过低的能量部分。
    - stability_preference (float): 0.0到1.0之间，越高越倾向于选择能量更稳定的片段。
    - num_segments (int): 需要返回的推荐片段数量。

    返回:
    - list of tuples: 每个元组包含 (start_time, end_time, score) 的列表。
    """
    try:
        y, sr = librosa.load(audio_path, sr=None, mono=True)
    except Exception as e:
        print(f"Error loading audio: {e}")
        return []

    if len(y) == 0:
        return []

    # 1. 移除开头和结尾的明显静音
    y_trimmed, index = librosa.effects.trim(y, top_db=25)

    if len(y_trimmed) == 0:
        print("Warning: Audio seems to be mostly silent after trimming.")
        if librosa.get_duration(y=y, sr=sr) >= target_duration_sec:
            y_trimmed = y
            index = (0, len(y))
        else:
            return []

    duration_trimmed = librosa.get_duration(y=y_trimmed, sr=sr)

    if duration_trimmed <= target_duration_sec:
        start_time_original = index[0] / sr
        end_time_original = index[1] / sr
        return [(start_time_original, end_time_original, 1.0)]

    # 2. 计算RMS能量
    hop_length = 512
    rms = librosa.feature.rms(y=y_trimmed, hop_length=hop_length)[0]

    # 3. 确定目标片段的帧数
    target_frames = librosa.time_to_frames(target_duration_sec, sr=sr, hop_length=hop_length)

    if target_frames >= len(rms):
        start_time_original = index[0] / sr
        end_time_original = index[1] / sr
        return [(start_time_original, end_time_original, 1.0)]

    # 4. 查找多个最佳片段
    segments = []
    max_rms = np.max(rms) if len(rms) > 0 else 0
    min_avg_energy = max_rms * energy_threshold_ratio

    # 使用滑动窗口查找所有可能的片段
    for i in range(0, len(rms) - target_frames + 1):
        segment_rms = rms[i : i + target_frames]
        
        avg_energy = np.mean(segment_rms)
        std_dev_energy = np.std(segment_rms)

        if avg_energy < min_avg_energy:
            continue

        score = (avg_energy * (1 - stability_preference)) - (std_dev_energy * stability_preference)
        
        # 将帧转换回时间
        start_time_in_trimmed = librosa.frames_to_time(i, sr=sr, hop_length=hop_length)
        end_time_in_trimmed = librosa.frames_to_time(i + target_frames, sr=sr, hop_length=hop_length)
        
        # 加上原始音频中被trim掉的开头部分时长
        original_start_offset_sec = index[0] / sr
        final_start_time = original_start_offset_sec + start_time_in_trimmed
        final_end_time = original_start_offset_sec + end_time_in_trimmed
        
        segments.append((final_start_time, final_end_time, score))

    # 5. 按分数排序并选择前N个不重叠的片段
    segments.sort(key=lambda x: x[2], reverse=True)
    selected_segments = []
    
    for segment in segments:
        if len(selected_segments) >= num_segments:
            break
            
        # 检查是否与已选片段重叠
        overlap = False
        for selected in selected_segments:
            if (segment[0] < selected[1] and segment[1] > selected[0]):
                overlap = True
                break
                
        if not overlap:
            selected_segments.append(segment)
    
    return selected_segments

# --- GUI部分 ---
class App(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("BGM最佳片段选取器")
        self.geometry("800x600")
        ctk.set_appearance_mode("System")
        ctk.set_default_color_theme("blue")

        self.audio_path = None
        self.current_segments = []
        self.current_sr = None
        self.playback_thread = None
        self.stop_playback_flag = threading.Event()

        # --- Widgets ---
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.pack(pady=20, padx=20, fill="both", expand=True)

        self.label_file = ctk.CTkLabel(self.main_frame, text="选择BGM文件:")
        self.label_file.grid(row=0, column=0, padx=10, pady=10, sticky="w")

        self.entry_file_path = ctk.CTkEntry(self.main_frame, width=300, state="readonly")
        self.entry_file_path.grid(row=0, column=1, padx=10, pady=10)

        self.button_browse = ctk.CTkButton(self.main_frame, text="浏览", command=self.browse_file)
        self.button_browse.grid(row=0, column=2, padx=10, pady=10)

        self.label_duration = ctk.CTkLabel(self.main_frame, text="期望片段时长 (秒):")
        self.label_duration.grid(row=1, column=0, padx=10, pady=10, sticky="w")
        
        self.duration_var = ctk.StringVar(value="20")
        self.entry_duration = ctk.CTkEntry(self.main_frame, textvariable=self.duration_var, width=80)
        self.entry_duration.grid(row=1, column=1, padx=10, pady=10, sticky="w")

        self.button_analyze = ctk.CTkButton(self.main_frame, text="分析并选取片段", command=self.analyze_and_select)
        self.button_analyze.grid(row=2, column=0, columnspan=3, padx=10, pady=15)

        # 创建片段显示区域
        self.segments_frame = ctk.CTkScrollableFrame(self.main_frame, width=700, height=300)
        self.segments_frame.grid(row=3, column=0, columnspan=3, padx=10, pady=10, sticky="nsew")
        
        self.status_label = ctk.CTkLabel(self, text="")
        self.status_label.pack(pady=(0, 10))
        
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def browse_file(self):
        filepath = filedialog.askopenfilename(
            title="选择BGM文件",
            filetypes=(("Audio Files", "*.mp3 *.wav *.aac *.flac *.m4a"), ("All files", "*.*"))
        )
        if filepath:
            self.audio_path = filepath
            self.entry_file_path.configure(state="normal")
            self.entry_file_path.delete(0, ctk.END)
            self.entry_file_path.insert(0, os.path.basename(filepath))
            self.entry_file_path.configure(state="readonly")
            self.clear_segments_display()
            self.status_label.configure(text="")

    def clear_segments_display(self):
        for widget in self.segments_frame.winfo_children():
            widget.destroy()

    def create_segment_widgets(self, segments):
        self.clear_segments_display()
        
        for i, (start_time, end_time, score) in enumerate(segments, 1):
            segment_frame = ctk.CTkFrame(self.segments_frame)
            segment_frame.pack(fill="x", padx=5, pady=5)
            
            # 片段信息
            info_frame = ctk.CTkFrame(segment_frame)
            info_frame.pack(fill="x", padx=5, pady=5)
            
            ctk.CTkLabel(info_frame, text=f"片段 {i}").pack(side="left", padx=5)
            ctk.CTkLabel(info_frame, text=f"开始: {start_time:.2f}秒").pack(side="left", padx=5)
            ctk.CTkLabel(info_frame, text=f"结束: {end_time:.2f}秒").pack(side="left", padx=5)
            ctk.CTkLabel(info_frame, text=f"时长: {end_time-start_time:.2f}秒").pack(side="left", padx=5)
            ctk.CTkLabel(info_frame, text=f"评分: {score:.2f}").pack(side="left", padx=5)
            
            # 控制按钮
            button_frame = ctk.CTkFrame(segment_frame)
            button_frame.pack(fill="x", padx=5, pady=5)
            
            play_button = ctk.CTkButton(
                button_frame, 
                text="播放", 
                command=lambda s=start_time, e=end_time: self.play_segment(s, e)
            )
            play_button.pack(side="left", padx=5)
            
            stop_button = ctk.CTkButton(
                button_frame, 
                text="停止", 
                command=self.stop_playing_segment
            )
            stop_button.pack(side="left", padx=5)
            
            download_button = ctk.CTkButton(
                button_frame, 
                text="下载", 
                command=lambda s=start_time, e=end_time: self.download_segment(s, e)
            )
            download_button.pack(side="left", padx=5)

    def analyze_and_select(self):
        if not self.audio_path:
            messagebox.showerror("错误", "请先选择一个BGM文件。")
            return

        try:
            target_duration = float(self.duration_var.get())
            if target_duration <= 0:
                messagebox.showerror("错误", "期望时长必须大于0。")
                return
        except ValueError:
            messagebox.showerror("错误", "期望时长必须是一个数字。")
            return

        self.status_label.configure(text="正在分析，请稍候...")
        self.update_idletasks()

        segments = find_best_bgm_segments(self.audio_path, target_duration_sec=target_duration)
        
        self.status_label.configure(text="")
        
        if segments:
            self.current_segments = segments
            self.create_segment_widgets(segments)
        else:
            messagebox.showinfo("结果", "未能找到合适的BGM片段。请尝试其他文件或调整参数。")
            self.clear_segments_display()

    def play_segment(self, start_time, end_time):
        if self.playback_thread and self.playback_thread.is_alive():
            self.stop_playing_segment()
            self.after(100, lambda: self._start_playback(start_time, end_time))
        else:
            self._start_playback(start_time, end_time)

    def _start_playback(self, start_time, end_time):
        try:
            segment_data, sr = librosa.load(
                self.audio_path,
                sr=None,
                offset=start_time,
                duration=end_time-start_time
            )
            
            self.current_segment_data = segment_data
            self.current_sr = sr
            
            self.playback_thread = threading.Thread(
                target=self._play_audio_task,
                daemon=True
            )
            self.playback_thread.start()
            
        except Exception as e:
            messagebox.showerror("错误", f"加载音频片段失败: {e}")

    def _play_audio_task(self):
        if self.current_segment_data is not None and self.current_sr is not None:
            try:
                self.stop_playback_flag.clear()
                
                block_size = int(self.current_sr * 0.1)
                total_frames = len(self.current_segment_data)
                
                with sd.OutputStream(samplerate=self.current_sr, channels=self.current_segment_data.ndim, dtype=self.current_segment_data.dtype) as stream:
                    for i in range(0, total_frames, block_size):
                        if self.stop_playback_flag.is_set():
                            stream.abort(ignore_errors=True)
                            break
                        end_idx = min(i + block_size, total_frames)
                        stream.write(self.current_segment_data[i:end_idx])
                        
            except Exception as e:
                messagebox.showerror("播放错误", f"播放音频时出错: {e}")

    def stop_playing_segment(self):
        if self.playback_thread and self.playback_thread.is_alive():
            self.stop_playback_flag.set()

    def download_segment(self, start_time, end_time):
        if not self.audio_path:
            return
            
        try:
            # 加载音频片段
            segment_data, sr = librosa.load(
                self.audio_path,
                sr=None,
                offset=start_time,
                duration=end_time-start_time
            )
            
            # 生成输出文件名
            base_name = os.path.splitext(os.path.basename(self.audio_path))[0]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"{base_name}_segment_{start_time:.1f}-{end_time:.1f}_{timestamp}.wav"
            
            # 选择保存位置
            save_path = filedialog.asksaveasfilename(
                defaultextension=".wav",
                initialfile=output_filename,
                filetypes=[("WAV files", "*.wav")]
            )
            
            if save_path:
                # 保存音频文件
                sf.write(save_path, segment_data, sr)
                messagebox.showinfo("成功", f"片段已保存到:\n{save_path}")
                
        except Exception as e:
            messagebox.showerror("错误", f"保存音频片段失败: {e}")

    def on_closing(self):
        self.stop_playing_segment()
        self.destroy()

if __name__ == "__main__":
    app = App()
    app.mainloop()