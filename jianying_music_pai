

import tkinter as tk
from tkinter import filedialog
from tkinter import ttk
import librosa
import librosa.display
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pygame
import threading
import time
import numpy as np

class BeatDetectorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("音频节奏点识别工具")
        self.audio_path = None
        self.y = None
        self.sr = None
        self.beat_times = []
        self.playing = False
        self.start_time = 0

        self.setup_ui()
        pygame.mixer.init()

    def setup_ui(self):
        self.root.configure(bg="#1e1e1e")
        style = ttk.Style()
        style.theme_use('default')
        style.configure('TButton', font=('Helvetica', 12), foreground='white', background='#2d2d2d', padding=10)
        style.map('TButton', background=[('active', '#3e3e3e')])

        title = tk.Label(self.root, text="剪映风格节奏点识别工具", font=("Helvetica", 18, "bold"), bg="#1e1e1e", fg="white")
        title.pack(pady=(20, 10))

        self.select_button = ttk.Button(self.root, text="🎵 选择音频文件", command=self.load_audio)
        self.select_button.pack(pady=10)

        separator = ttk.Separator(self.root, orient='horizontal')
        separator.pack(fill='x', pady=5)

        self.plot_frame = tk.Frame(self.root, bg="#1e1e1e")
        self.plot_frame.pack(pady=10)

        self.play_button = ttk.Button(self.root, text="▶ 播放预览", command=self.toggle_play, state=tk.DISABLED)
        self.play_button.pack(pady=10)

    def load_audio(self):
        file_path = filedialog.askopenfilename(filetypes=[("Audio Files", "*.mp3 *.wav")])
        if file_path:
            self.audio_path = file_path
            self.y, self.sr = librosa.load(file_path)
            self.beat_times = self.detect_beats(self.y, self.sr)
            self.plot_waveform_with_beats()
            self.play_button.state(['!disabled'])

    def detect_beats(self, y, sr):
        tempo, beat_frames = librosa.beat.beat_track(y=y, sr=sr)
        return librosa.frames_to_time(beat_frames, sr=sr)

    def plot_waveform_with_beats(self):
        plt.clf()
        fig, ax = plt.subplots(figsize=(10, 3))
        librosa.display.waveshow(self.y, sr=self.sr, alpha=0.6, ax=ax)
        ax.vlines(self.beat_times, -1, 1, color='r', linestyle='--', label='Beats')
        ax.set_title("节奏点（踩点）可视化")
        ax.legend()
        if hasattr(self, 'canvas'):
            self.canvas.get_tk_widget().destroy()
        self.canvas = FigureCanvasTkAgg(fig, master=self.plot_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack()

    def toggle_play(self):
        if not self.playing:
            pygame.mixer.music.load(self.audio_path)
            pygame.mixer.music.play()
            self.playing = True
            self.start_time = time.time()
            self.play_button.config(text="停止播放")
            # 禁用选择音频按钮
            self.select_button.state(['disabled'])
            threading.Thread(target=self.update_cursor, daemon=True).start()
        else:
            pygame.mixer.music.stop()
            self.playing = False
            self.play_button.config(text="▶ 播放预览")
            # 恢复选择音频按钮
            self.select_button.state(['!disabled'])

    def update_cursor(self):
        while self.playing and pygame.mixer.music.get_busy():
            elapsed = time.time() - self.start_time
            self.plot_waveform_with_beats()
            plt.axvline(x=elapsed, color='g', linestyle='-', label='当前播放位置')
            self.canvas.draw()
            time.sleep(0.1)

if __name__ == "__main__":
    root = tk.Tk()
    app = BeatDetectorApp(root)
    root.mainloop()