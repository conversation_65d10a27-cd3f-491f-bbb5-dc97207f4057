import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import librosa
import numpy as np
import soundfile as sf
import os
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pygame

class BeatDetectorGUI:
    def __init__(self, master):
        self.master = master
        master.title("音乐节拍踩点识别")
        master.geometry("800x600")

        # 初始化pygame用于音频播放
        pygame.mixer.init()

        # 创建主框架
        main_frame = ttk.Frame(master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="1. 选择音频文件", padding="5")
        file_frame.pack(fill=tk.X, pady=5)

        self.file_path = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file_path, width=50).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_frame, text="浏览", command=self.browse_file).pack(side=tk.LEFT, padx=5)

        # 节拍设置区域
        beat_frame = ttk.LabelFrame(main_frame, text="2. 节拍设置", padding="5")
        beat_frame.pack(fill=tk.X, pady=5)

        ttk.Label(beat_frame, text="节拍精度:").pack(side=tk.LEFT, padx=5)
        self.beat_precision = tk.StringVar(value="8")
        ttk.Radiobutton(beat_frame, text="8拍", variable=self.beat_precision, 
                       value="8").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(beat_frame, text="16拍", variable=self.beat_precision, 
                       value="16").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(beat_frame, text="剪映踩点", variable=self.beat_precision, 
                       value="jianying").pack(side=tk.LEFT, padx=5)

        # 处理按钮
        process_frame = ttk.Frame(main_frame)
        process_frame.pack(fill=tk.X, pady=5)
        self.process_button = ttk.Button(process_frame, text="检测节拍", 
                                       command=self.detect_beats, state=tk.DISABLED)
        self.process_button.pack(pady=5)

        # 波形显示区域
        self.fig, self.ax = plt.subplots(figsize=(10, 3))
        self.canvas = FigureCanvasTkAgg(self.fig, master=main_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, pady=5)

        # 播放控制区域
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)
        
        self.play_button = ttk.Button(control_frame, text="播放", 
                                    command=self.play_audio, state=tk.DISABLED)
        self.play_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="停止", 
                                    command=self.stop_audio, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)

        # 状态显示
        self.status_var = tk.StringVar(value="状态: 就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.pack(side=tk.BOTTOM, fill=tk.X, pady=5)

        # 存储音频数据
        self.audio_data = None
        self.sample_rate = None
        self.beat_times = None
        self.is_playing = False
        self.playback_indicator = None # 播放进度指示器
        self.update_indicator_job = None # 用于取消定期更新的任务

    def browse_file(self):
        filetypes = (
            ("音频文件", "*.wav *.mp3 *.flac"),
            ("所有文件", "*.*")
        )
        filename = filedialog.askopenfilename(
            title="选择音频文件",
            filetypes=filetypes
        )
        if filename:
            self.file_path.set(filename)
            self.process_button.config(state=tk.NORMAL)
            self.status_var.set("状态: 已选择文件")

    def detect_beats(self):
        if not self.file_path.get():
            messagebox.showwarning("警告", "请先选择音频文件")
            return

        try:
            self.status_var.set("状态: 正在分析音频...")
            self.master.update_idletasks()

            # 加载音频文件
            self.audio_data, self.sample_rate = librosa.load(self.file_path.get())
            
            # 检测节拍
            tempo, beat_frames = librosa.beat.beat_track(y=self.audio_data, sr=self.sample_rate)
            self.beat_times = librosa.frames_to_time(beat_frames, sr=self.sample_rate)

            # 根据选择的精度调整节拍
            precision_type = self.beat_precision.get()
            if precision_type == "8":
                # 8拍模式，使用原始节拍点 (librosa默认)
                pass # 保持原始的 beat_times
            elif precision_type == "16":
                # 16拍模式，在每两个节拍之间插入一个节拍点
                new_beat_times = []
                for i in range(len(self.beat_times) - 1):
                    new_beat_times.append(self.beat_times[i])
                    new_beat_times.append((self.beat_times[i] + self.beat_times[i + 1]) / 2)
                new_beat_times.append(self.beat_times[-1])
                self.beat_times = np.array(new_beat_times)
            elif precision_type == "jianying":
                 # 剪映踩点，直接使用librosa的原始节拍点，与8拍模式相同逻辑，但保留选项区分
                 pass # 保持原始的 beat_times

            # 绘制波形和节拍点
            self.plot_audio_and_beats()

            # 启用播放控制
            self.play_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.NORMAL)

            self.status_var.set(f"状态: 检测完成 - 检测到 {len(self.beat_times)} 个节拍点")
            
            # 保存节拍信息到文本文件
            self.save_beat_info()

        except Exception as e:
            messagebox.showerror("错误", f"处理音频时出错：\n{str(e)}")
            self.status_var.set("状态: 处理失败")

    def plot_audio_and_beats(self):
        self.ax.clear()
        
        # 绘制音频波形
        time = np.arange(len(self.audio_data)) / self.sample_rate
        self.ax.plot(time, self.audio_data, color='gray', alpha=0.5)
        
        # 绘制节拍点
        for beat_time in self.beat_times:
            # self.ax.axvline(x=beat_time, color='r', alpha=0.5) # 原来的红色竖线标记
            # 绘制蓝色的圆点标记
            self.ax.plot(beat_time, 0, marker='o', color='blue', markersize=5, linestyle='None', alpha=0.8)
        
        self.ax.set_xlabel('时间 (秒)')
        self.ax.set_ylabel('振幅')
        self.ax.set_title('音频波形和节拍点')
        self.ax.grid(True)
        
        # 创建播放进度指示器 (初始不可见)
        self.playback_indicator = self.ax.axvline(x=0, color='gold', linestyle='--', linewidth=1.5, alpha=0)

        self.canvas.draw()

        # 绑定鼠标滚轮事件
        self.canvas.mpl_connect('scroll_event', self.on_scroll)

        # 绑定鼠标拖动事件
        self.canvas.mpl_connect('button_press_event', self.on_button_press)
        self.canvas.mpl_connect('button_release_event', self.on_button_release)
        self.canvas.mpl_connect('motion_notify_event', self.on_mouse_motion)

        # 拖动状态变量
        self.is_panning = False
        self.pan_start_x = None

    def on_button_press(self, event):
        """处理鼠标按下事件，开始拖动"""
        # 只响应左键，并且鼠标在图表区域内
        if event.button == 1 and event.inaxes == self.ax:
            self.is_panning = True
            # 记录按下时的鼠标位置（数据坐标）
            self.pan_start_x = event.xdata
            # 改变鼠标样式表示可拖动（可选）
            # self.canvas.get_tk_widget().config(cursor="fleur")

    def on_button_release(self, event):
        """处理鼠标释放事件，结束拖动"""
        if self.is_panning:
            self.is_panning = False
            self.pan_start_x = None
            # 恢复鼠标样式（可选）
            # self.canvas.get_tk_widget().config(cursor="")

    def on_mouse_motion(self, event):
        """处理鼠标移动事件，进行拖动"""
        # 如果正在拖动且鼠标在图表区域内
        if self.is_panning and event.inaxes == self.ax:
            # 获取当前鼠标位置（数据坐标）
            current_x = event.xdata
            if current_x is None or self.pan_start_x is None:
                return

            # 计算鼠标移动的距离（在数据坐标系中）
            dx = current_x - self.pan_start_x

            # 获取当前的x轴范围
            cur_xlim = self.ax.get_xlim()

            # 计算新的x轴范围
            new_xlim = [cur_xlim[0] - dx, cur_xlim[1] - dx]

            # 限制新的x轴范围在音频时长内
            min_xlim = 0
            max_xlim = len(self.audio_data) / self.sample_rate if self.audio_data is not None else cur_xlim[1]

            # 确保范围不超出边界
            if new_xlim[0] < min_xlim:
                offset = min_xlim - new_xlim[0]
                new_xlim = [min_xlim, new_xlim[1] + offset]
            if new_xlim[1] > max_xlim:
                offset = new_xlim[1] - max_xlim
                new_xlim = [new_xlim[0] - offset, max_xlim]

            # 如果音频很短，可能无法拖动
            if new_xlim[1] - new_xlim[0] > max_xlim - min_xlim and max_xlim > min_xlim:
                 new_xlim = [min_xlim, max_xlim]

            # 应用新的x轴范围并重新绘制
            self.ax.set_xlim(new_xlim)
            self.canvas.draw_idle()

            # 更新拖动起始位置，实现连续拖动
            self.pan_start_x = current_x

    def on_scroll(self, event):
        """处理鼠标滚轮事件进行缩放"""
        base_scale = 1.1 # 缩放因子

        # 获取当前x轴范围
        cur_xlim = self.ax.get_xlim()
        
        # 获取鼠标在图表中的位置（数据坐标）
        xdata = event.xdata # 鼠标事件的x数据坐标
        
        if xdata is None: # 如果鼠标不在图表区域内，不进行缩放
            return

        # 计算新的x轴范围
        # event.button > 0 表示向上滚动（放大），< 0 表示向下滚动（缩小）
        if event.button == 'up':
            # 放大
            scale_factor = 1 / base_scale
        elif event.button == 'down':
            # 缩小
            scale_factor = base_scale
        else:
            # 其他滚轮事件（例如水平滚动），忽略
            return

        # 确保不会无限放大或缩小
        min_xlim = 0 # 音频开始时间
        max_xlim = len(self.audio_data) / self.sample_rate if self.audio_data is not None else cur_xlim[1] # 音频结束时间

        # 计算缩放后的新范围，以鼠标位置为中心
        new_ch = (cur_xlim[1] - cur_xlim[0]) * scale_factor
        relx = (xdata - cur_xlim[0]) / (cur_xlim[1] - cur_xlim[0])
        new_xlim = [xdata - new_ch * relx, xdata + new_ch * (1 - relx)]

        # 限制新的x轴范围在音频时长内
        new_xlim[0] = max(min_xlim, new_xlim[0])
        new_xlim[1] = min(max_xlim, new_xlim[1])

        # 防止范围过小或过大导致问题
        if new_xlim[1] - new_xlim[0] < 1e-3: # 最小显示范围，可调整
            new_xlim[0] = xdata - 0.0005
            new_xlim[1] = xdata + 0.0005
        if new_xlim[0] < min_xlim:
             new_xlim[0] = min_xlim
        if new_xlim[1] > max_xlim:
             new_xlim[1] = max_xlim

        # 应用新的x轴范围并重新绘制
        self.ax.set_xlim(new_xlim)
        self.canvas.draw_idle()

    def play_audio(self):
        if not self.is_playing:
            # 获取当前图表显示的起始时间作为播放起点
            start_time = self.ax.get_xlim()[0]
            if start_time < 0:
                start_time = 0

            try:
                pygame.mixer.music.load(self.file_path.get())
                # 从计算出的起始时间开始播放
                pygame.mixer.music.play(start=start_time)
                self.is_playing = True
                self.play_button.config(text="暂停") # 播放中显示暂停

                # 启动进度指示器更新
                self.playback_indicator.set_alpha(1) # 使指示器可见
                self.update_playback_indicator()

            except pygame.error as e:
                messagebox.showerror("播放错误", f"无法播放音频：{e}")
                self.is_playing = False
                self.play_button.config(text="播放")
        else:
            if pygame.mixer.music.get_busy():
                pygame.mixer.music.pause()
                self.is_playing = False
                self.play_button.config(text="继续")
                # 暂停时取消进度指示器更新
                if self.update_indicator_job:
                    self.master.after_cancel(self.update_indicator_job)
                    self.update_indicator_job = None
            else:
                pygame.mixer.music.unpause()
                self.is_playing = True
                self.play_button.config(text="暂停")
                # 继续时重新启动进度指示器更新
                self.update_playback_indicator()

    def stop_audio(self):
        pygame.mixer.music.stop()
        self.is_playing = False
        self.play_button.config(text="播放")
        # 停止时隐藏进度指示器并取消更新
        if self.playback_indicator:
            self.playback_indicator.set_alpha(0)
            self.canvas.draw_idle()
        if self.update_indicator_job:
            self.master.after_cancel(self.update_indicator_job)
            self.update_indicator_job = None

    def update_playback_indicator(self):
        """定期更新播放进度指示器的位置"""
        if self.is_playing and pygame.mixer.music.get_busy():
            # 获取当前播放时间 (秒)
            current_pos = pygame.mixer.music.get_pos() / 1000.0

            # 获取当前图表显示的X轴范围
            cur_xlim = self.ax.get_xlim()
            min_xlim, max_xlim = cur_xlim

            # 如果播放位置超出当前显示范围，自动调整X轴
            if current_pos < min_xlim or current_pos > max_xlim:
                # 计算新的中心点，尝试保持当前视图比例
                view_width = max_xlim - min_xlim
                new_center = current_pos # 将当前播放位置作为新的中心点
                new_xlim = [new_center - view_width / 2, new_center + view_width / 2]

                # 确保新的范围在音频总时长内
                audio_duration = len(self.audio_data) / self.sample_rate if self.audio_data is not None else max_xlim
                if new_xlim[0] < 0:
                    new_xlim = [0, view_width]
                if new_xlim[1] > audio_duration:
                    new_xlim = [audio_duration - view_width, audio_duration]
                if new_xlim[0] < 0: # 处理音频时长小于视图宽度的情况
                     new_xlim = [0, audio_duration]

                self.ax.set_xlim(new_xlim)

            # 更新指示器位置
            self.playback_indicator.set_xdata([current_pos, current_pos])
            self.canvas.draw_idle()

            # 每隔50毫秒更新一次指示器位置
            self.update_indicator_job = self.master.after(50, self.update_playback_indicator)
        else:
            # 播放停止或暂停时，隐藏指示器
            if self.playback_indicator:
                self.playback_indicator.set_alpha(0)
                self.canvas.draw_idle()
            if self.update_indicator_job:
                self.master.after_cancel(self.update_indicator_job)
                self.update_indicator_job = None

    def save_beat_info(self):
        # 生成输出文件名
        base_name = os.path.splitext(self.file_path.get())[0]
        output_file = f"{base_name}_beats.txt"
        
        # 保存节拍信息
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"节拍精度: {self.beat_precision.get()}拍\n")
            f.write(f"总节拍数: {len(self.beat_times)}\n")
            f.write("\n节拍时间点 (秒):\n")
            for i, beat_time in enumerate(self.beat_times):
                f.write(f"{i+1}. {beat_time:.3f}\n")
        
        messagebox.showinfo("成功", f"节拍信息已保存至：\n{output_file}")

if __name__ == "__main__":
    root = tk.Tk()
    app = BeatDetectorGUI(root)
    root.mainloop()
