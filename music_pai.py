import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import librosa
import numpy as np
import soundfile as sf
import os
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pygame

class BeatDetectorGUI:
    def __init__(self, master):
        self.master = master
        master.title("音乐节拍踩点识别")
        master.geometry("800x600")

        # 初始化pygame用于音频播放
        pygame.mixer.init()

        # 创建主框架
        main_frame = ttk.Frame(master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="1. 选择音频文件", padding="5")
        file_frame.pack(fill=tk.X, pady=5)

        self.file_path = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file_path, width=50).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_frame, text="浏览", command=self.browse_file).pack(side=tk.LEFT, padx=5)

        # 节拍设置区域
        beat_frame = ttk.LabelFrame(main_frame, text="2. 节拍设置", padding="5")
        beat_frame.pack(fill=tk.X, pady=5)

        ttk.Label(beat_frame, text="节拍精度:").pack(side=tk.LEFT, padx=5)
        self.beat_precision = tk.StringVar(value="8")
        ttk.Radiobutton(beat_frame, text="8拍", variable=self.beat_precision, 
                       value="8").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(beat_frame, text="16拍", variable=self.beat_precision, 
                       value="16").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(beat_frame, text="剪映踩点", variable=self.beat_precision, 
                       value="jianying").pack(side=tk.LEFT, padx=5)

        # 处理按钮
        process_frame = ttk.Frame(main_frame)
        process_frame.pack(fill=tk.X, pady=5)
        self.process_button = ttk.Button(process_frame, text="检测节拍", 
                                       command=self.detect_beats, state=tk.DISABLED)
        self.process_button.pack(pady=5)

        # 波形显示区域
        self.fig, self.ax = plt.subplots(figsize=(10, 3))
        self.canvas = FigureCanvasTkAgg(self.fig, master=main_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, pady=5)

        # 播放控制区域
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)
        
        self.play_button = ttk.Button(control_frame, text="播放", 
                                    command=self.play_audio, state=tk.DISABLED)
        self.play_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="停止", 
                                    command=self.stop_audio, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)

        # 状态显示
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=5)

        self.status_var = tk.StringVar(value="状态: 就绪")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT)

        # 快捷键提示
        shortcut_label = ttk.Label(status_frame,
                                 text="快捷键: 空格-播放/暂停 | ESC-停止 | F-全屏 | ↑↓-缩放 | ←→-移动 | 双击-智能缩放",
                                 font=('Arial', 8), foreground='gray')
        shortcut_label.pack(side=tk.RIGHT)

        # 存储音频数据
        self.audio_data = None
        self.sample_rate = None
        self.beat_times = None
        self.is_playing = False
        self.playback_indicator = None # 播放进度指示器
        self.update_indicator_job = None # 用于取消定期更新的任务

    def browse_file(self):
        filetypes = (
            ("音频文件", "*.wav *.mp3 *.flac"),
            ("所有文件", "*.*")
        )
        filename = filedialog.askopenfilename(
            title="选择音频文件",
            filetypes=filetypes
        )
        if filename:
            self.file_path.set(filename)
            self.process_button.config(state=tk.NORMAL)
            self.status_var.set("状态: 已选择文件")

    def detect_beats(self):
        if not self.file_path.get():
            messagebox.showwarning("警告", "请先选择音频文件")
            return

        try:
            self.status_var.set("状态: 正在分析音频...")
            self.master.update_idletasks()

            # 加载音频文件
            self.audio_data, self.sample_rate = librosa.load(self.file_path.get())
            
            # 检测节拍
            tempo, beat_frames = librosa.beat.beat_track(y=self.audio_data, sr=self.sample_rate)
            self.beat_times = librosa.frames_to_time(beat_frames, sr=self.sample_rate)

            # 根据选择的精度调整节拍
            precision_type = self.beat_precision.get()
            if precision_type == "8":
                # 8拍模式，使用原始节拍点 (librosa默认)
                pass # 保持原始的 beat_times
            elif precision_type == "16":
                # 16拍模式，在每两个节拍之间插入一个节拍点
                new_beat_times = []
                for i in range(len(self.beat_times) - 1):
                    new_beat_times.append(self.beat_times[i])
                    new_beat_times.append((self.beat_times[i] + self.beat_times[i + 1]) / 2)
                new_beat_times.append(self.beat_times[-1])
                self.beat_times = np.array(new_beat_times)
            elif precision_type == "jianying":
                 # 剪映踩点，直接使用librosa的原始节拍点，与8拍模式相同逻辑，但保留选项区分
                 pass # 保持原始的 beat_times

            # 绘制波形和节拍点
            self.plot_audio_and_beats()

            # 启用播放控制
            self.play_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.NORMAL)

            self.status_var.set(f"状态: 检测完成 - 检测到 {len(self.beat_times)} 个节拍点")
            
            # 保存节拍信息到文本文件
            self.save_beat_info()

        except Exception as e:
            messagebox.showerror("错误", f"处理音频时出错：\n{str(e)}")
            self.status_var.set("状态: 处理失败")

    def plot_audio_and_beats(self):
        self.ax.clear()

        # 设置背景色
        self.ax.set_facecolor('#1e1e1e')  # 深色背景，类似剪映

        # 绘制音频波形 - 使用渐变效果
        time = np.arange(len(self.audio_data)) / self.sample_rate

        # 绘制波形填充区域
        self.ax.fill_between(time, self.audio_data, 0, color='#4a9eff', alpha=0.3, linewidth=0)
        # 绘制波形线条
        self.ax.plot(time, self.audio_data, color='#4a9eff', linewidth=0.8, alpha=0.8)

        # 绘制节拍点 - 使用更醒目的样式
        for beat_time in self.beat_times:
            # 绘制节拍线
            self.ax.axvline(x=beat_time, color='#ff6b6b', alpha=0.7, linewidth=1.5, linestyle='-')
            # 在顶部添加节拍标记
            y_max = np.max(np.abs(self.audio_data)) if len(self.audio_data) > 0 else 1
            self.ax.plot(beat_time, y_max * 0.9, marker='v', color='#ff6b6b',
                        markersize=6, linestyle='None', alpha=0.9)

        # 设置坐标轴样式
        self.ax.set_xlabel('时间 (秒)', color='white', fontsize=10)
        self.ax.set_ylabel('振幅', color='white', fontsize=10)
        self.ax.set_title('音频波形和节拍点', color='white', fontsize=12, pad=20)

        # 设置网格样式
        self.ax.grid(True, color='#404040', alpha=0.3, linewidth=0.5)

        # 设置坐标轴颜色
        self.ax.tick_params(colors='white', labelsize=9)
        self.ax.spines['bottom'].set_color('#606060')
        self.ax.spines['top'].set_color('#606060')
        self.ax.spines['left'].set_color('#606060')
        self.ax.spines['right'].set_color('#606060')

        # 创建播放进度指示器 - 更醒目的样式
        self.playback_indicator = self.ax.axvline(x=0, color='#ffd700', linestyle='-',
                                                linewidth=2, alpha=0, zorder=10)

        # 设置图表背景
        self.fig.patch.set_facecolor('#2d2d2d')

        self.canvas.draw()

        # 绑定鼠标事件
        self.canvas.mpl_connect('scroll_event', self.on_scroll)
        self.canvas.mpl_connect('button_press_event', self.on_button_press)
        self.canvas.mpl_connect('button_release_event', self.on_button_release)
        self.canvas.mpl_connect('motion_notify_event', self.on_mouse_motion)

        # 绑定键盘事件
        self.canvas.mpl_connect('key_press_event', self.on_key_press)

        # 确保canvas可以接收键盘焦点
        self.canvas.get_tk_widget().focus_set()

        # 交互状态变量
        self.is_panning = False
        self.pan_start_x = None
        self.pan_start_xlim = None
        self.last_click_time = 0
        self.double_click_threshold = 0.3  # 双击时间阈值（秒）

    def on_button_press(self, event):
        """处理鼠标按下事件，支持拖动、点击定位和双击缩放"""
        if event.button == 1 and event.inaxes == self.ax and event.xdata is not None:
            import time
            current_time = time.time()

            # 检测双击
            if current_time - self.last_click_time < self.double_click_threshold:
                self.on_double_click(event)
                self.last_click_time = 0  # 重置，避免三击被误认为双击
                return

            self.last_click_time = current_time

            # 开始拖动
            self.is_panning = True
            self.pan_start_x = event.xdata
            self.pan_start_xlim = self.ax.get_xlim()

            # 改变鼠标样式
            self.canvas.get_tk_widget().config(cursor="fleur")

    def on_button_release(self, event):
        """处理鼠标释放事件，结束拖动或执行点击定位"""
        if self.is_panning:
            # 检查是否为点击（没有明显拖动）
            if (event.xdata is not None and self.pan_start_x is not None and
                abs(event.xdata - self.pan_start_x) < 0.1):  # 0.1秒内的移动视为点击
                self.on_click_seek(event.xdata)

            self.is_panning = False
            self.pan_start_x = None
            self.pan_start_xlim = None

            # 恢复鼠标样式
            self.canvas.get_tk_widget().config(cursor="")

    def on_mouse_motion(self, event):
        """处理鼠标移动事件，进行流畅拖动"""
        if self.is_panning and event.xdata is not None and self.pan_start_x is not None:
            # 计算相对于初始点击位置的偏移
            dx = event.xdata - self.pan_start_x

            # 基于初始视图范围计算新范围，避免累积误差
            if self.pan_start_xlim is not None:
                new_xlim = [self.pan_start_xlim[0] - dx, self.pan_start_xlim[1] - dx]

                # 获取音频边界
                min_xlim = 0
                max_xlim = len(self.audio_data) / self.sample_rate if self.audio_data is not None else new_xlim[1]

                # 平滑的边界限制
                view_width = new_xlim[1] - new_xlim[0]
                if new_xlim[0] < min_xlim:
                    new_xlim = [min_xlim, min_xlim + view_width]
                elif new_xlim[1] > max_xlim:
                    new_xlim = [max_xlim - view_width, max_xlim]

                # 确保不超出音频总长度
                if view_width > max_xlim - min_xlim:
                    new_xlim = [min_xlim, max_xlim]

                # 应用新范围
                self.ax.set_xlim(new_xlim)
                self.canvas.draw_idle()

    def on_scroll(self, event):
        """处理鼠标滚轮事件进行智能缩放"""
        if event.xdata is None:
            return

        # 动态缩放因子，根据当前缩放级别调整
        cur_xlim = self.ax.get_xlim()
        current_range = cur_xlim[1] - cur_xlim[0]

        # 根据当前范围调整缩放速度
        if current_range > 60:  # 大范围时快速缩放
            base_scale = 1.3
        elif current_range > 10:  # 中等范围时中速缩放
            base_scale = 1.2
        else:  # 小范围时精细缩放
            base_scale = 1.1

        # 确定缩放方向
        if event.button == 'up':
            scale_factor = 1 / base_scale  # 放大
        elif event.button == 'down':
            scale_factor = base_scale      # 缩小
        else:
            return

        # 获取音频边界
        min_xlim = 0
        max_xlim = len(self.audio_data) / self.sample_rate if self.audio_data is not None else cur_xlim[1]

        # 以鼠标位置为中心进行缩放
        mouse_x = event.xdata
        left_ratio = (mouse_x - cur_xlim[0]) / current_range
        right_ratio = (cur_xlim[1] - mouse_x) / current_range

        new_range = current_range * scale_factor

        # 计算新的边界
        new_left = mouse_x - new_range * left_ratio
        new_right = mouse_x + new_range * right_ratio

        # 智能边界处理
        if new_left < min_xlim:
            new_left = min_xlim
            new_right = min_xlim + new_range
        if new_right > max_xlim:
            new_right = max_xlim
            new_left = max_xlim - new_range

        # 防止过度缩放
        min_range = 0.01  # 最小显示范围10毫秒
        max_range = max_xlim - min_xlim  # 最大显示范围为整个音频

        if new_range < min_range:
            center = (new_left + new_right) / 2
            new_left = center - min_range / 2
            new_right = center + min_range / 2
        elif new_range > max_range:
            new_left = min_xlim
            new_right = max_xlim

        # 最终边界检查
        new_left = max(min_xlim, new_left)
        new_right = min(max_xlim, new_right)

        # 应用新范围
        self.ax.set_xlim([new_left, new_right])
        self.canvas.draw_idle()

    def on_double_click(self, event):
        """处理双击事件，智能缩放到合适范围"""
        if event.xdata is None:
            return

        # 获取当前范围
        cur_xlim = self.ax.get_xlim()
        current_range = cur_xlim[1] - cur_xlim[0]

        # 智能缩放逻辑
        if current_range > 30:  # 如果当前范围很大，缩放到中等范围
            new_range = 10
        elif current_range > 5:  # 如果是中等范围，缩放到精细范围
            new_range = 2
        else:  # 如果已经很精细，则缩放到全局视图
            max_xlim = len(self.audio_data) / self.sample_rate if self.audio_data is not None else cur_xlim[1]
            new_range = max_xlim

        # 以双击位置为中心设置新范围
        center = event.xdata
        new_left = center - new_range / 2
        new_right = center + new_range / 2

        # 边界处理
        min_xlim = 0
        max_xlim = len(self.audio_data) / self.sample_rate if self.audio_data is not None else cur_xlim[1]

        if new_left < min_xlim:
            new_left = min_xlim
            new_right = min_xlim + new_range
        if new_right > max_xlim:
            new_right = max_xlim
            new_left = max_xlim - new_range

        # 确保不超出边界
        new_left = max(min_xlim, new_left)
        new_right = min(max_xlim, new_right)

        self.ax.set_xlim([new_left, new_right])
        self.canvas.draw_idle()

    def on_click_seek(self, seek_time):
        """处理点击定位，跳转播放位置"""
        if self.audio_data is not None and pygame.mixer.get_init():
            # 如果正在播放，先停止
            if self.is_playing:
                pygame.mixer.music.stop()
                self.is_playing = False
                self.play_button.config(text="播放")

            # 更新播放指示器位置
            if self.playback_indicator:
                self.playback_indicator.set_xdata([seek_time, seek_time])
                self.playback_indicator.set_alpha(0.8)
                self.canvas.draw_idle()

            # 可以在这里添加从指定位置开始播放的逻辑
            # 由于pygame的限制，这里只是视觉上的定位
            self.status_var.set(f"状态: 定位到 {seek_time:.2f} 秒")

    def on_key_press(self, event):
        """处理键盘快捷键"""
        if event.key == ' ':  # 空格键播放/暂停
            if self.play_button['state'] != tk.DISABLED:
                self.play_audio()
        elif event.key == 'escape':  # ESC键停止
            if self.stop_button['state'] != tk.DISABLED:
                self.stop_audio()
        elif event.key == 'f':  # F键适应全屏
            self.fit_to_view()
        elif event.key == 'left':  # 左箭头键向左移动
            self.pan_view(-0.5)
        elif event.key == 'right':  # 右箭头键向右移动
            self.pan_view(0.5)
        elif event.key == 'up':  # 上箭头键放大
            self.zoom_at_center(0.8)
        elif event.key == 'down':  # 下箭头键缩小
            self.zoom_at_center(1.25)

    def fit_to_view(self):
        """适应全屏显示整个音频"""
        if self.audio_data is not None:
            max_xlim = len(self.audio_data) / self.sample_rate
            self.ax.set_xlim([0, max_xlim])
            self.canvas.draw_idle()

    def pan_view(self, direction):
        """平移视图"""
        cur_xlim = self.ax.get_xlim()
        view_width = cur_xlim[1] - cur_xlim[0]
        pan_distance = view_width * 0.1 * direction  # 移动10%的视图宽度

        new_xlim = [cur_xlim[0] + pan_distance, cur_xlim[1] + pan_distance]

        # 边界检查
        min_xlim = 0
        max_xlim = len(self.audio_data) / self.sample_rate if self.audio_data is not None else cur_xlim[1]

        if new_xlim[0] < min_xlim:
            new_xlim = [min_xlim, min_xlim + view_width]
        elif new_xlim[1] > max_xlim:
            new_xlim = [max_xlim - view_width, max_xlim]

        self.ax.set_xlim(new_xlim)
        self.canvas.draw_idle()

    def zoom_at_center(self, scale_factor):
        """在视图中心进行缩放"""
        cur_xlim = self.ax.get_xlim()
        center = (cur_xlim[0] + cur_xlim[1]) / 2
        current_range = cur_xlim[1] - cur_xlim[0]
        new_range = current_range * scale_factor

        new_xlim = [center - new_range/2, center + new_range/2]

        # 边界检查
        min_xlim = 0
        max_xlim = len(self.audio_data) / self.sample_rate if self.audio_data is not None else cur_xlim[1]

        if new_xlim[0] < min_xlim:
            new_xlim[0] = min_xlim
        if new_xlim[1] > max_xlim:
            new_xlim[1] = max_xlim

        # 防止过度缩放
        if new_xlim[1] - new_xlim[0] < 0.01:
            return
        if new_xlim[1] - new_xlim[0] > max_xlim - min_xlim:
            new_xlim = [min_xlim, max_xlim]

        self.ax.set_xlim(new_xlim)
        self.canvas.draw_idle()

    def play_audio(self):
        if not self.is_playing:
            # 获取当前图表显示的起始时间作为播放起点
            start_time = self.ax.get_xlim()[0]
            if start_time < 0:
                start_time = 0

            try:
                pygame.mixer.music.load(self.file_path.get())
                # 从计算出的起始时间开始播放
                pygame.mixer.music.play(start=start_time)
                self.is_playing = True
                self.play_button.config(text="暂停") # 播放中显示暂停

                # 启动进度指示器更新
                self.playback_indicator.set_alpha(1) # 使指示器可见
                self.update_playback_indicator()

            except pygame.error as e:
                messagebox.showerror("播放错误", f"无法播放音频：{e}")
                self.is_playing = False
                self.play_button.config(text="播放")
        else:
            if pygame.mixer.music.get_busy():
                pygame.mixer.music.pause()
                self.is_playing = False
                self.play_button.config(text="继续")
                # 暂停时取消进度指示器更新
                if self.update_indicator_job:
                    self.master.after_cancel(self.update_indicator_job)
                    self.update_indicator_job = None
            else:
                pygame.mixer.music.unpause()
                self.is_playing = True
                self.play_button.config(text="暂停")
                # 继续时重新启动进度指示器更新
                self.update_playback_indicator()

    def stop_audio(self):
        pygame.mixer.music.stop()
        self.is_playing = False
        self.play_button.config(text="播放")
        # 停止时隐藏进度指示器并取消更新
        if self.playback_indicator:
            self.playback_indicator.set_alpha(0)
            self.canvas.draw_idle()
        if self.update_indicator_job:
            self.master.after_cancel(self.update_indicator_job)
            self.update_indicator_job = None

    def update_playback_indicator(self):
        """定期更新播放进度指示器的位置"""
        if self.is_playing and pygame.mixer.music.get_busy():
            # 获取当前播放时间 (秒)
            current_pos = pygame.mixer.music.get_pos() / 1000.0

            # 获取当前图表显示的X轴范围
            cur_xlim = self.ax.get_xlim()
            min_xlim, max_xlim = cur_xlim

            # 如果播放位置超出当前显示范围，自动调整X轴
            if current_pos < min_xlim or current_pos > max_xlim:
                # 计算新的中心点，尝试保持当前视图比例
                view_width = max_xlim - min_xlim
                new_center = current_pos # 将当前播放位置作为新的中心点
                new_xlim = [new_center - view_width / 2, new_center + view_width / 2]

                # 确保新的范围在音频总时长内
                audio_duration = len(self.audio_data) / self.sample_rate if self.audio_data is not None else max_xlim
                if new_xlim[0] < 0:
                    new_xlim = [0, view_width]
                if new_xlim[1] > audio_duration:
                    new_xlim = [audio_duration - view_width, audio_duration]
                if new_xlim[0] < 0: # 处理音频时长小于视图宽度的情况
                     new_xlim = [0, audio_duration]

                self.ax.set_xlim(new_xlim)

            # 更新指示器位置
            self.playback_indicator.set_xdata([current_pos, current_pos])
            self.canvas.draw_idle()

            # 每隔50毫秒更新一次指示器位置
            self.update_indicator_job = self.master.after(50, self.update_playback_indicator)
        else:
            # 播放停止或暂停时，隐藏指示器
            if self.playback_indicator:
                self.playback_indicator.set_alpha(0)
                self.canvas.draw_idle()
            if self.update_indicator_job:
                self.master.after_cancel(self.update_indicator_job)
                self.update_indicator_job = None

    def save_beat_info(self):
        # 生成输出文件名
        base_name = os.path.splitext(self.file_path.get())[0]
        output_file = f"{base_name}_beats.txt"
        
        # 保存节拍信息
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"节拍精度: {self.beat_precision.get()}拍\n")
            f.write(f"总节拍数: {len(self.beat_times)}\n")
            f.write("\n节拍时间点 (秒):\n")
            for i, beat_time in enumerate(self.beat_times):
                f.write(f"{i+1}. {beat_time:.3f}\n")
        
        messagebox.showinfo("成功", f"节拍信息已保存至：\n{output_file}")

if __name__ == "__main__":
    root = tk.Tk()
    app = BeatDetectorGUI(root)
    root.mainloop()
