import numpy as np
import soundfile as sf # 推荐使用 soundfile
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import pyloudnorm as pyln
import torch
from demucs.pretrained import get_model
from demucs.apply import apply_model
from voicefixer import VoiceFixer
# MDX-Net 相关导入
try:
    import onnxruntime as ort
    import librosa
    import json
    import hashlib
    MDX_NET_AVAILABLE = True
    print("MDX-Net 依赖已加载")
except ImportError as e:
    print(f"警告：MDX-Net 相关模块未找到: {e}")
    MDX_NET_AVAILABLE = False

# 新增MDX23C模型导入
try:
    from ultimatevocalremovergui.lib_v5.tfc_tdf_v3 import TFC_TDF_net
    MDX23C_AVAILABLE = True
except ImportError:
    print("警告：MDX23C 相关模块未找到，MDX23C 功能将不可用")
    MDX23C_AVAILABLE = False

def peak_normalize_audio_single_param(audio_data, target_dbfs=-0.1):
    """
    对音频数据进行峰值归一化，使用单一目标峰值参数。

    参数:
    audio_data (np.ndarray): 音频数据。期望是浮点数 [-1.0, 1.0] 或会自动转换。
                             如果是多声道，期望形状为 (n_samples, n_channels)。
    target_dbfs (float): 目标峰值电平，单位 dBFS。
                         默认值为 -0.1 dBFS，这是一个推荐的"最佳配置"。

    返回:
    np.ndarray: 归一化后的音频数据，数据类型与输入尽可能保持一致（优先浮点）。
    """

    # 1. 确定数据类型并转换为浮点数 [-1.0, 1.0]
    #    这个逻辑可以更健壮，参考上一个答案的详细版本
    #    这里简化假设，如果不是float，则尝试转换
    original_dtype = audio_data.dtype
    is_integer_input = np.issubdtype(original_dtype, np.integer)

    if is_integer_input:
        if original_dtype == np.int16:
            audio_float = audio_data.astype(np.float32) / 32768.0
        elif original_dtype == np.int32: # 假设是全范围的int32
            audio_float = audio_data.astype(np.float32) / (2**31 -1) # 2147483647.0
        elif original_dtype == np.uint8:
            audio_float = (audio_data.astype(np.float32) - 128.0) / 128.0
        else: # 其他整数类型，或需要更精确的位数信息
            print(f"Warning: Integer type {original_dtype} might not be scaled correctly. Prefer float input or use a more robust conversion.")
            # 尝试一个通用（但不完美）的转换
            iinfo = np.iinfo(original_dtype)
            audio_float = audio_data.astype(np.float32) / float(max(abs(iinfo.min), iinfo.max))
    elif np.issubdtype(original_dtype, np.floating):
        audio_float = audio_data.astype(np.float32) # 确保是 float32 进行计算
    else:
        raise ValueError(f"Unsupported audio_data dtype: {original_dtype}")

    # 2. 找到当前峰值 (线性)
    current_peak_linear = np.max(np.abs(audio_float))

    # 3. 处理静音或接近静音的情况
    if current_peak_linear < 1e-9: # 阈值可以调整
        # print("Audio is silent or near silent, no normalization applied.")
        if is_integer_input:
            return np.zeros_like(audio_data, dtype=original_dtype)
        return audio_float 

    # 4. 计算目标线性峰值
    target_peak_linear = 10**(target_dbfs / 20.0)

    # 5. 计算增益因子
    gain = target_peak_linear / current_peak_linear
    
    # 6. 应用增益
    normalized_audio_float = audio_float * gain

    # 7. (可选但推荐) 削波保护，确保严格在 [-1.0, 1.0] 内
    normalized_audio_float = np.clip(normalized_audio_float, -1.0, 1.0)

    # 8. 转换回原始整数类型 (如果输入是整数)
    if is_integer_input:
        if original_dtype == np.int16:
            final_audio = np.round(normalized_audio_float * 32767.0).astype(np.int16)
        elif original_dtype == np.int32:
            final_audio = np.round(normalized_audio_float * (2**31 - 1)).astype(np.int32)
        elif original_dtype == np.uint8:
            final_audio = np.round((normalized_audio_float * 127.0) + 128.0).astype(np.uint8)
            final_audio = np.clip(final_audio, 0, 255)
        else:
            # 对于其他未明确处理的整数类型，返回浮点数
            print(f"Warning: Returning float32 for unhandled original integer dtype {original_dtype}")
            return normalized_audio_float.astype(np.float32)
        return final_audio
    else:
        # 如果输入是浮点数，直接返回处理后的浮点数 (保持原始浮点类型 float32/float64)
        return normalized_audio_float.astype(original_dtype)

def lufs_normalize_audio(audio_data, sample_rate, target_lufs=-16.0):
    """
    对音频数据进行 LUFS 归一化。

    参数:
    audio_data (np.ndarray): 音频数据。期望是浮点数 [-1.0, 1.0] 或会自动转换。
                             如果是多声道，期望形状为 (n_samples, n_channels)。
    sample_rate (int): 采样率
    target_lufs (float): 目标 LUFS 值，默认为 -16.0 LUFS

    返回:
    np.ndarray: 归一化后的音频数据
    """
    # 确保音频数据是浮点数
    if not np.issubdtype(audio_data.dtype, np.floating):
        audio_data = audio_data.astype(np.float32)
        if np.issubdtype(audio_data.dtype, np.integer):
            if audio_data.dtype == np.int16:
                audio_data = audio_data / 32768.0
            elif audio_data.dtype == np.int32:
                audio_data = audio_data / (2**31 - 1)
            elif audio_data.dtype == np.uint8:
                audio_data = (audio_data - 128.0) / 128.0

    # 创建 LUFS 计量器
    meter = pyln.Meter(sample_rate)
    
    # 计算当前 LUFS 值
    current_lufs = meter.integrated_loudness(audio_data)
    
    # 计算增益
    gain_db = target_lufs - current_lufs
    gain_linear = 10 ** (gain_db / 20.0)
    
    # 应用增益
    normalized_audio = audio_data * gain_linear
    
    # 确保不削波
    normalized_audio = np.clip(normalized_audio, -1.0, 1.0)
    
    return normalized_audio

def extract_vocals_demucs_v4(audio_data, sample_rate):
    """
    使用 Demucs v4 模型提取人声。

    参数:
    audio_data (np.ndarray): 音频数据 (samples, channels) 或 (samples,)
    sample_rate (int): 采样率

    返回:
    np.ndarray: 提取的人声数据
    """
    print("开始使用 Demucs v4 提取人声...")
    try:
        if len(audio_data.shape) == 1:
            audio_data = np.stack([audio_data, audio_data])
        elif audio_data.shape[1] == 1:
            audio_data = np.repeat(audio_data, 2, axis=1)
        
        print("加载 Demucs v4 模型...")
        model = get_model('htdemucs')
        model.eval()
        
        # 将音频数据转换为模型所需的格式
        audio_tensor = torch.from_numpy(audio_data).float()
        if len(audio_tensor.shape) == 2:
            audio_tensor = audio_tensor.unsqueeze(0)
        audio_tensor = audio_tensor.permute(0, 2, 1)  # 转换为 (batch, channels, time)
        
        print("处理音频...")
        with torch.no_grad():
            # 使用模型分离音频
            separated = apply_model(model, audio_tensor, device='cpu')
            # 只保留人声轨道
            vocals = separated[0, 3]  # 获取人声轨道
            
        # 转换回numpy数组并确保格式正确
        vocals = vocals.numpy()

        # 确保输出格式正确 (samples, channels)
        if len(vocals.shape) == 1:
            vocals = vocals.reshape(-1, 1)
        elif vocals.shape[0] == 2 and vocals.shape[1] > vocals.shape[0]:
            # 如果是 (channels, samples) 格式，转换为 (samples, channels)
            vocals = vocals.T

        # 清理内存
        del audio_tensor, separated
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        return vocals
        
    except Exception as e:
        print(f"使用 Demucs v4 处理音频时出错: {str(e)}")
        raise

def extract_vocals_htdemucs(audio_data, sample_rate):
    """
    使用 HTDemucs 模型提取人声。

    参数:
    audio_data (np.ndarray): 音频数据 (samples, channels) 或 (samples,)
    sample_rate (int): 采样率

    返回:
    np.ndarray: 提取的人声数据
    """
    print("开始使用 HTDemucs 提取人声...")
    try:
        if len(audio_data.shape) == 1:
            audio_data = np.stack([audio_data, audio_data])
        elif audio_data.shape[1] == 1:
            audio_data = np.repeat(audio_data, 2, axis=1)
        
        print("加载 HTDemucs 模型...")
        model = get_model('htdemucs_ft')
        model.eval()
        
        # 将音频数据转换为模型所需的格式
        audio_tensor = torch.from_numpy(audio_data).float()
        if len(audio_tensor.shape) == 2:
            audio_tensor = audio_tensor.unsqueeze(0)
        audio_tensor = audio_tensor.permute(0, 2, 1)  # 转换为 (batch, channels, time)
        
        print("处理音频...")
        with torch.no_grad():
            # 使用模型分离音频
            separated = apply_model(model, audio_tensor, device='cpu')
            # 只保留人声轨道
            vocals = separated[0, 3]  # 获取人声轨道
            
        # 转换回numpy数组并确保格式正确
        vocals = vocals.numpy()

        # 确保输出格式正确 (samples, channels)
        if len(vocals.shape) == 1:
            vocals = vocals.reshape(-1, 1)
        elif vocals.shape[0] == 2 and vocals.shape[1] > vocals.shape[0]:
            # 如果是 (channels, samples) 格式，转换为 (samples, channels)
            vocals = vocals.T

        # 清理内存
        del audio_tensor, separated
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        return vocals
        
    except Exception as e:
        print(f"使用 HTDemucs 处理音频时出错: {str(e)}")
        raise

def extract_vocals_mdx_net(audio_data, sample_rate, model_name="UVR-MDX-NET-Voc_FT"):
    """
    使用MDX-Net模型提取人声。支持多个模型，有自动回退机制。

    参数:
    audio_data (np.ndarray): 音频数据 (samples, channels) 或 (samples,)
    sample_rate (int): 采样率
    model_name (str): 模型名称，默认为"UVR-MDX-NET-Voc_FT"

    返回:
    np.ndarray: 提取的人声数据
    """
    print(f"开始使用MDX-Net模型 {model_name} 提取人声...")

    if not MDX_NET_AVAILABLE:
        raise ImportError("MDX-Net 功能不可用。请安装必要的依赖：pip install onnxruntime librosa")

    # 定义可用的模型列表（按优先级排序）
    available_models = [
        model_name,  # 用户指定的模型
        "UVR-MDX-NET-Inst_HQ_5",  # 备选模型1
        "UVR-MDX-NET-Inst_HQ_4",  # 备选模型2
        "UVR-MDX-NET-Inst_HQ_3",  # 备选模型3
    ]

    # 去重，保持顺序
    models_to_try = []
    for model in available_models:
        if model not in models_to_try:
            models_to_try.append(model)

    last_error = None

    for current_model in models_to_try:
        try:
            print(f"尝试使用模型: {current_model}")

            # 确保模型文件存在
            model_path = ensure_mdx_model_exists(current_model)

            # 预处理音频
            processed_audio = preprocess_audio_for_mdx(audio_data, sample_rate)

            # 加载并运行模型
            vocals = run_mdx_inference(processed_audio, model_path, current_model)

            # 后处理
            vocals = postprocess_mdx_output(vocals, audio_data.shape)

            print(f"MDX-Net {current_model} 人声提取完成")
            return vocals

        except Exception as e:
            print(f"模型 {current_model} 失败: {str(e)}")
            last_error = e
            continue

    # 如果所有模型都失败了
    raise Exception(f"所有MDX-Net模型都失败了。最后的错误: {last_error}")

def ensure_mdx_model_exists(model_name):
    """确保MDX模型文件存在，如果不存在则下载"""
    model_dir = "/Users/<USER>/Cursor2026/bgm-pre/ultimatevocalremovergui/models/MDX_Net_Models"
    model_path = os.path.join(model_dir, f"{model_name}.onnx")

    if os.path.exists(model_path):
        print(f"找到模型文件: {model_path}")
        return model_path

    # 如果模型不存在，尝试下载
    print(f"模型文件 {model_name}.onnx 不存在，尝试下载...")

    # 创建模型目录
    os.makedirs(model_dir, exist_ok=True)

    # 下载模型 - 使用正确的GitHub链接
    download_url = f"https://github.com/TRvlvr/model_repo/releases/download/all_public_uvr_models/{model_name}.onnx"

    try:
        import urllib.request
        print(f"正在从 {download_url} 下载模型...")
        urllib.request.urlretrieve(download_url, model_path)
        print(f"模型下载完成: {model_path}")
        return model_path
    except Exception as e:
        raise FileNotFoundError(f"无法下载模型 {model_name}: {str(e)}")

def preprocess_audio_for_mdx(audio_data, sample_rate):
    """为MDX模型预处理音频数据"""
    # 确保音频是双声道
    if len(audio_data.shape) == 1:
        audio_data = np.stack([audio_data, audio_data], axis=1)
    elif audio_data.shape[1] == 1:
        audio_data = np.repeat(audio_data, 2, axis=1)

    # 重采样到44100Hz（MDX模型的标准采样率）
    if sample_rate != 44100:
        print(f"重采样音频从 {sample_rate}Hz 到 44100Hz")
        audio_resampled = []
        for channel in range(audio_data.shape[1]):
            resampled_channel = librosa.resample(
                audio_data[:, channel],
                orig_sr=sample_rate,
                target_sr=44100
            )
            audio_resampled.append(resampled_channel)
        audio_data = np.column_stack(audio_resampled)

    # 归一化到[-1, 1]范围
    max_val = np.max(np.abs(audio_data))
    if max_val > 0:
        audio_data = audio_data / max_val

    return audio_data.astype(np.float32)

def run_mdx_inference(audio_data, model_path, model_name):
    """运行MDX模型推理"""
    print(f"加载ONNX模型: {model_path}")

    # 创建ONNX Runtime会话
    providers = ['CPUExecutionProvider']
    if torch.cuda.is_available():
        providers.insert(0, 'CUDAExecutionProvider')

    session = ort.InferenceSession(model_path, providers=providers)

    # 获取模型输入输出信息
    input_info = session.get_inputs()[0]
    output_info = session.get_outputs()[0]

    input_name = input_info.name
    output_name = output_info.name
    expected_shape = input_info.shape

    print(f"模型输入名称: {input_name}")
    print(f"模型输出名称: {output_name}")
    print(f"期望输入形状: {expected_shape}")

    # 根据模型期望的输入形状调整音频处理
    vocals = perform_mdx_separation_adaptive(audio_data, session, input_name, output_name, expected_shape, model_name)

    return vocals

def perform_mdx_separation_adaptive(audio_data, session, input_name, output_name, expected_shape, model_name):
    """执行自适应MDX分离，根据模型期望的输入形状调整处理"""
    print("执行自适应MDX分离...")

    # 根据期望形状确定参数
    if len(expected_shape) >= 4:
        expected_channels = expected_shape[1] if expected_shape[1] != -1 else 2
        expected_freq = expected_shape[2] if expected_shape[2] != -1 else 3072
        expected_time = expected_shape[3] if expected_shape[3] != -1 else 256
    else:
        # 默认参数
        expected_channels = 2
        expected_freq = 3072
        expected_time = 256

    print(f"期望维度 - 声道: {expected_channels}, 频率: {expected_freq}, 时间: {expected_time}")

    # 根据期望频率维度计算n_fft
    n_fft = (expected_freq - 1) * 2

    # 计算hop_length以获得期望的时间维度
    audio_length = audio_data.shape[0]
    hop_length = max(1, audio_length // expected_time)

    print(f"使用参数 - n_fft: {n_fft}, hop_length: {hop_length}")

    try:
        # 对每个声道执行STFT
        stft_data = []
        for channel in range(min(audio_data.shape[1], expected_channels)):
            stft = librosa.stft(
                audio_data[:, channel],
                n_fft=n_fft,
                hop_length=hop_length,
                window='hann'
            )
            stft_data.append(stft)

        # 如果需要更多声道，复制现有声道
        while len(stft_data) < expected_channels:
            stft_data.append(stft_data[-1])

        # 合并声道 (channels, freq, time)
        stft_combined = np.stack(stft_data[:expected_channels], axis=0)

        # 调整频率和时间维度
        stft_combined = adjust_stft_dimensions(stft_combined, expected_freq, expected_time)

        # 转换为幅度和相位
        magnitude = np.abs(stft_combined)
        phase = np.angle(stft_combined)

        # 准备模型输入 (batch, channels, freq, time)
        model_input = magnitude[np.newaxis, :, :, :].astype(np.float32)

        print(f"调整后模型输入形状: {model_input.shape}")

        # 运行推理
        print("运行模型推理...")
        output = session.run([output_name], {input_name: model_input})[0]

        print(f"模型输出形状: {output.shape}")

        # 处理输出
        vocals = process_mdx_output(output, phase, hop_length, audio_data.shape)

        return vocals

    except Exception as e:
        print(f"自适应MDX分离失败: {e}")
        # 回退到简单的中心声道提取
        print("回退到简单的中心声道提取")
        if audio_data.shape[1] >= 2:
            vocals = (audio_data[:, 0] + audio_data[:, 1]) / 2
            return vocals.reshape(-1, 1)
        else:
            return audio_data

def adjust_stft_dimensions(stft_data, target_freq, target_time):
    """调整STFT数据的频率和时间维度"""
    current_freq, current_time = stft_data.shape[1], stft_data.shape[2]

    # 调整频率维度
    if current_freq != target_freq:
        if current_freq > target_freq:
            # 截断
            stft_data = stft_data[:, :target_freq, :]
        else:
            # 填充
            padding = np.zeros((stft_data.shape[0], target_freq - current_freq, stft_data.shape[2]), dtype=stft_data.dtype)
            stft_data = np.concatenate([stft_data, padding], axis=1)

    # 调整时间维度
    if current_time != target_time:
        if current_time > target_time:
            # 截断
            stft_data = stft_data[:, :, :target_time]
        else:
            # 填充
            padding = np.zeros((stft_data.shape[0], stft_data.shape[1], target_time - current_time), dtype=stft_data.dtype)
            stft_data = np.concatenate([stft_data, padding], axis=2)

    return stft_data

def process_mdx_output(output, original_phase, hop_length, original_audio_shape):
    """处理MDX模型输出"""
    try:
        # 移除batch维度
        if len(output.shape) == 4:
            output_magnitude = output[0]
        else:
            output_magnitude = output

        print(f"MDX输出形状: {output_magnitude.shape}")

        # 尝试两种策略：
        # 策略1: 直接使用模型输出作为频谱幅度
        # 策略2: 使用模型输出作为掩码

        # 计算每个通道的能量
        channel_energies = []
        for i in range(output_magnitude.shape[0]):
            energy = np.mean(output_magnitude[i] ** 2)
            channel_energies.append(energy)
            print(f"通道 {i} 能量: {energy:.6f}")

        # 对于UVR-MDX-NET-Voc_FT模型，通常：
        # 通道0: 人声
        # 通道1: 伴奏/乐器
        # 我们直接使用通道0作为人声

        vocals_magnitude = output_magnitude[0]  # 直接使用通道0
        print(f"使用通道0作为人声，形状: {vocals_magnitude.shape}")

        # 调整相位维度以匹配输出
        phase_channel = original_phase[0]  # 使用第一个声道的相位
        if phase_channel.shape != vocals_magnitude.shape:
            phase_channel = adjust_phase_dimensions(phase_channel, vocals_magnitude.shape)

        # 直接使用模型输出作为幅度谱，结合原始相位
        vocals_complex_spec = vocals_magnitude * np.exp(1j * np.angle(phase_channel))

        # 执行逆STFT
        print("执行逆STFT变换...")
        try:
            vocals_audio = librosa.istft(
                vocals_complex_spec,
                hop_length=hop_length,
                window='hann'
            )
        except Exception as e:
            print(f"逆STFT失败: {e}")
            # 使用简单的方法
            vocals_audio = np.real(np.fft.ifft(vocals_complex_spec.flatten()))
            vocals_audio = vocals_audio[:original_audio_shape[0]]

        # 确保输出是正确的形状
        if len(vocals_audio.shape) == 1:
            vocals = vocals_audio.reshape(-1, 1)
        else:
            vocals = vocals_audio

        print(f"最终人声输出形状: {vocals.shape}")

        return vocals

    except Exception as e:
        print(f"处理MDX输出失败: {e}")
        # 返回静音
        return np.zeros((original_audio_shape[0], 1), dtype=np.float32)

def adjust_phase_dimensions(phase, target_shape):
    """调整相位数据维度"""
    if phase.shape == target_shape:
        return phase

    # 简单的维度调整
    if phase.shape[0] > target_shape[0]:
        phase = phase[:target_shape[0], :]
    elif phase.shape[0] < target_shape[0]:
        padding = np.zeros((target_shape[0] - phase.shape[0], phase.shape[1]))
        phase = np.concatenate([phase, padding], axis=0)

    if phase.shape[1] > target_shape[1]:
        phase = phase[:, :target_shape[1]]
    elif phase.shape[1] < target_shape[1]:
        padding = np.zeros((phase.shape[0], target_shape[1] - phase.shape[1]))
        phase = np.concatenate([phase, padding], axis=1)

    return phase

def postprocess_mdx_output(vocals, original_shape):
    """后处理MDX输出"""
    print(f"后处理前形状: {vocals.shape}")

    # 确保输出格式正确
    if len(vocals.shape) == 1:
        vocals = vocals.reshape(-1, 1)

    # 如果输出是多声道但原始音频需要单声道，转换为单声道
    if len(original_shape) == 1 or (len(original_shape) == 2 and original_shape[1] == 1):
        if vocals.shape[1] > 1:
            vocals = np.mean(vocals, axis=1, keepdims=True)
    elif len(original_shape) == 2 and original_shape[1] == 2:
        # 如果原始音频是立体声，但输出是单声道，复制为立体声
        if vocals.shape[1] == 1:
            vocals = np.repeat(vocals, 2, axis=1)

    # 归一化输出到合理范围
    max_val = np.max(np.abs(vocals))
    if max_val > 1.0:
        vocals = vocals / max_val * 0.95  # 留一点余量避免削波
    elif max_val < 0.01:  # 如果信号太小，可能是静音
        print("警告：输出信号很小，可能是静音")

    print(f"后处理后形状: {vocals.shape}, 最大值: {max_val:.4f}")

    return vocals.astype(np.float32)



def extract_vocals_mdx23c(audio_data, sample_rate):
    """
    使用MDX23C模型提取人声。
    这是一个独立的实现，使用深度学习风格的频谱处理。

    参数:
    audio_data (np.ndarray): 音频数据 (samples, channels) 或 (samples,)
    sample_rate (int): 采样率

    返回:
    np.ndarray: 提取的人声数据
    """
    print("开始使用MDX23C深度频谱处理算法提取人声...")

    try:
        # 确保音频数据格式正确
        if len(audio_data.shape) == 1:
            audio_data = np.stack([audio_data, audio_data], axis=1)
        elif audio_data.shape[1] == 1:
            audio_data = np.repeat(audio_data, 2, axis=1)

        print("执行深度频谱处理...")

        # 使用深度学习风格的频谱处理技术
        vocals = deep_spectral_vocal_extraction(audio_data, sample_rate)

        print("MDX23C深度频谱处理完成")
        return vocals

    except Exception as e:
        print(f"使用MDX23C处理音频时出错: {str(e)}")
        raise

def deep_spectral_vocal_extraction(audio_data, sample_rate):
    """
    使用深度学习风格的频谱处理提取人声
    """
    left_channel = audio_data[:, 0]
    right_channel = audio_data[:, 1]

    # 使用更大的FFT窗口以获得更好的频率分辨率
    n_fft = 4096
    hop_length = 1024

    # 对左右声道进行STFT
    stft_left = librosa.stft(left_channel, n_fft=n_fft, hop_length=hop_length)
    stft_right = librosa.stft(right_channel, n_fft=n_fft, hop_length=hop_length)

    # 计算幅度谱
    mag_left = np.abs(stft_left)
    mag_right = np.abs(stft_right)
    phase_left = np.angle(stft_left)

    # 计算立体声特征
    mono_mag = (mag_left + mag_right) / 2
    stereo_mag = np.abs(mag_left - mag_right)

    # 使用多层频谱掩码
    vocal_mask = compute_multi_layer_vocal_mask(mono_mag, stereo_mag, sample_rate)

    # 应用掩码
    vocal_mag = mono_mag * vocal_mask

    # 重建复数频谱
    vocal_stft = vocal_mag * np.exp(1j * phase_left)

    # 逆STFT
    vocals = librosa.istft(vocal_stft, hop_length=hop_length)

    # 后处理
    vocals = apply_mdx23c_postprocessing(vocals, sample_rate)

    return vocals.reshape(-1, 1)

def compute_multi_layer_vocal_mask(mono_mag, stereo_mag, sample_rate):
    """
    计算多层人声掩码
    """
    # 频率轴
    freqs = librosa.fft_frequencies(sr=sample_rate, n_fft=mono_mag.shape[0]*2-1)

    # 层1：基于频率的掩码（人声频率范围）
    freq_mask = np.ones_like(mono_mag)
    vocal_freq_range = (freqs >= 80) & (freqs <= 8000)
    freq_mask[~vocal_freq_range] *= 0.3

    # 层2：基于立体声信息的掩码
    stereo_ratio = stereo_mag / (mono_mag + 1e-10)
    stereo_mask = 1.0 / (1.0 + np.exp(-5 * (0.5 - stereo_ratio)))  # sigmoid函数

    # 层3：基于谐波结构的掩码
    harmonic_mask = compute_harmonic_mask(mono_mag, freqs)

    # 层4：基于时间连续性的掩码
    temporal_mask = compute_temporal_consistency_mask(mono_mag)

    # 组合所有掩码
    combined_mask = freq_mask * stereo_mask * harmonic_mask * temporal_mask

    # 平滑掩码
    from scipy import ndimage
    try:
        combined_mask = ndimage.gaussian_filter(combined_mask, sigma=1.0)
    except:
        pass  # 如果scipy不可用，跳过平滑

    return np.clip(combined_mask, 0.1, 1.0)

def compute_harmonic_mask(magnitude, freqs):
    """
    基于谐波结构计算掩码
    """
    # 寻找基频和谐波
    mask = np.ones_like(magnitude)

    # 在人声基频范围内增强
    fundamental_range = (freqs >= 80) & (freqs <= 400)
    mask[fundamental_range] *= 1.5

    # 增强谐波频率
    for harmonic in [2, 3, 4, 5]:
        harmonic_range = (freqs >= 80*harmonic) & (freqs <= 400*harmonic)
        if np.any(harmonic_range):
            mask[harmonic_range] *= 1.2

    return mask

def compute_temporal_consistency_mask(magnitude):
    """
    基于时间连续性计算掩码
    """
    # 计算时间维度的变化率
    temporal_diff = np.diff(magnitude, axis=1)
    temporal_stability = 1.0 / (1.0 + np.abs(temporal_diff))

    # 扩展到原始大小
    temporal_mask = np.ones_like(magnitude)
    temporal_mask[:, 1:] = temporal_stability

    return temporal_mask

def apply_mdx23c_postprocessing(audio, sample_rate):
    """
    应用MDX23C风格的后处理
    """
    try:
        from scipy import signal

        # 多段压缩器
        audio = apply_multiband_compressor(audio, sample_rate)

        # 去噪
        audio = apply_spectral_gating(audio, sample_rate)

    except ImportError:
        print("scipy不可用，跳过高级后处理")
    except Exception as e:
        print(f"后处理出错: {e}")

    return audio

def apply_multiband_compressor(audio, sample_rate):
    """
    应用多段压缩器
    """
    try:
        from scipy import signal

        # 分频点
        crossover_freqs = [200, 2000]

        # 设计滤波器
        nyquist = sample_rate / 2

        # 低频段
        low_b, low_a = signal.butter(4, crossover_freqs[0]/nyquist, btype='low')
        low_band = signal.filtfilt(low_b, low_a, audio)

        # 中频段
        mid_b, mid_a = signal.butter(4, [crossover_freqs[0]/nyquist, crossover_freqs[1]/nyquist], btype='band')
        mid_band = signal.filtfilt(mid_b, mid_a, audio)

        # 高频段
        high_b, high_a = signal.butter(4, crossover_freqs[1]/nyquist, btype='high')
        high_band = signal.filtfilt(high_b, high_a, audio)

        # 分别压缩每个频段
        low_compressed = compress_audio(low_band, threshold=0.6, ratio=2.0)
        mid_compressed = compress_audio(mid_band, threshold=0.5, ratio=3.0)
        high_compressed = compress_audio(high_band, threshold=0.7, ratio=2.5)

        # 重新组合
        return low_compressed + mid_compressed + high_compressed

    except:
        return audio

def compress_audio(audio, threshold=0.5, ratio=3.0):
    """
    简单的音频压缩器
    """
    audio_abs = np.abs(audio)
    mask = audio_abs > threshold
    compressed = audio.copy()
    compressed[mask] = np.sign(audio[mask]) * (threshold + (audio_abs[mask] - threshold) / ratio)
    return compressed

def apply_spectral_gating(audio, sample_rate):
    """
    应用频谱门控去噪
    """
    try:
        # 简单的频谱门控
        stft = librosa.stft(audio, n_fft=2048, hop_length=512)
        magnitude = np.abs(stft)
        phase = np.angle(stft)

        # 计算噪声阈值
        noise_threshold = np.percentile(magnitude, 20)

        # 应用门控
        gate_mask = magnitude > noise_threshold * 1.5
        gated_magnitude = magnitude * gate_mask

        # 重建
        gated_stft = gated_magnitude * np.exp(1j * phase)
        return librosa.istft(gated_stft, hop_length=512)

    except:
        return audio

def extract_vocals_demucs_6s(audio_data, sample_rate):
    """
    使用Demucs 6s模型提取人声。

    参数:
    audio_data (np.ndarray): 音频数据 (samples, channels) 或 (samples,)
    sample_rate (int): 采样率

    返回:
    np.ndarray: 提取的人声数据
    """
    print("开始使用Demucs 6s提取人声...")
    try:
        if len(audio_data.shape) == 1:
            audio_data = np.stack([audio_data, audio_data])
        elif audio_data.shape[1] == 1:
            audio_data = np.repeat(audio_data, 2, axis=1)
        
        print("加载Demucs 6s模型...")
        model = get_model('htdemucs_6s')
        model.eval()
        
        # 将音频数据转换为模型所需的格式
        audio_tensor = torch.from_numpy(audio_data).float()
        if len(audio_tensor.shape) == 2:
            audio_tensor = audio_tensor.unsqueeze(0)
        audio_tensor = audio_tensor.permute(0, 2, 1)  # 转换为 (batch, channels, time)
        
        print("处理音频...")
        with torch.no_grad():
            # 使用模型分离音频
            separated = apply_model(model, audio_tensor, device='cpu')
            # 只保留人声轨道
            vocals = separated[0, 3]  # 获取人声轨道
            
        # 转换回numpy数组并确保格式正确
        vocals = vocals.numpy()

        # 确保输出格式正确 (samples, channels)
        if len(vocals.shape) == 1:
            vocals = vocals.reshape(-1, 1)
        elif vocals.shape[0] == 2 and vocals.shape[1] > vocals.shape[0]:
            # 如果是 (channels, samples) 格式，转换为 (samples, channels)
            vocals = vocals.T

        # 清理内存
        del audio_tensor, separated
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        return vocals
        
    except Exception as e:
        print(f"使用Demucs 6s处理音频时出错: {str(e)}")
        raise

def fix_vocals_voicefixer(audio_data, sample_rate):
    """
    使用VoiceFixer修复人声。
    参数:
    audio_data (np.ndarray): 音频数据 (samples, channels) 或 (samples,)
    sample_rate (int): 采样率
    返回:
    np.ndarray: 修复后的人声数据
    """
    print("开始使用VoiceFixer修复人声...")
    try:
        # VoiceFixer 只支持 44100Hz，需重采样
        if sample_rate != 44100:
            import librosa
            if len(audio_data.shape) > 1:
                audio_data = librosa.resample(audio_data.T, orig_sr=sample_rate, target_sr=44100).T
            else:
                audio_data = librosa.resample(audio_data, orig_sr=sample_rate, target_sr=44100)
            sample_rate = 44100
        # VoiceFixer 只支持单声道
        if len(audio_data.shape) > 1 and audio_data.shape[1] > 1:
            audio_data = np.mean(audio_data, axis=1)
        # 保存为临时文件
        import tempfile
        import os
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_in, \
             tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_out:
            sf.write(tmp_in.name, audio_data, sample_rate)
            tmp_in.flush()
            model = VoiceFixer()
            # 尝试 mode=1 或 mode=2
            model.restore(input=tmp_in.name, output=tmp_out.name, cuda=False, mode=1)
            fixed_audio, _ = sf.read(tmp_out.name)
            os.remove(tmp_in.name)
            os.remove(tmp_out.name)
        return fixed_audio
    except Exception as e:
        print(f"使用VoiceFixer处理音频时出错: {str(e)}")
        raise

def extract_vocals_audio_sx(audio_data, sample_rate):
    """
    使用 AudioSx / x-minus.pro 提取人声。
    这是一个独立的实现，使用中心声道提取算法。

    参数:
    audio_data (np.ndarray): 音频数据 (samples, channels) 或 (samples,)
    sample_rate (int): 采样率
    返回:
    np.ndarray: 提取的人声数据
    """
    print("开始使用 AudioSx 中心声道提取算法...")

    try:
        # 确保音频数据是双声道
        if len(audio_data.shape) == 1:
            # 单声道，无法进行中心声道提取，直接返回
            print("输入为单声道音频，无法进行中心声道提取")
            return audio_data.reshape(-1, 1)
        elif audio_data.shape[1] == 1:
            # 单声道，无法进行中心声道提取，直接返回
            print("输入为单声道音频，无法进行中心声道提取")
            return audio_data
        else:
            # 双声道，使用中心声道提取算法
            print("执行中心声道提取算法...")
            left_channel = audio_data[:, 0]
            right_channel = audio_data[:, 1]

            # 中心声道提取：人声通常在立体声的中心
            # 方法1：简单平均
            center = (left_channel + right_channel) / 2

            # 方法2：侧声道提取（可选）
            # side = (left_channel - right_channel) / 2

            # 应用简单的高通滤波器来增强人声频率
            if sample_rate >= 16000:  # 只有在采样率足够高时才应用滤波
                from scipy import signal
                # 设计高通滤波器，截止频率约80Hz
                nyquist = sample_rate / 2
                cutoff = 80 / nyquist
                if cutoff < 1.0:
                    b, a = signal.butter(2, cutoff, btype='high')
                    center = signal.filtfilt(b, a, center)

            print("中心声道提取完成")
            return center.reshape(-1, 1)

    except Exception as e:
        print(f"使用 AudioSx 提取人声时出错: {str(e)}")
        raise

class AudioNormalizerGUI:
    def __init__(self, master):
        self.master = master
        master.title("音频处理工具")
        master.geometry("800x550")  # 增加窗口高度

        # 创建主框架
        main_frame = ttk.Frame(master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="1. 选择音频文件", padding="5")
        file_frame.pack(fill=tk.X, pady=5)

        self.file_path = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file_path, width=50).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_frame, text="浏览", command=self.browse_file).pack(side=tk.LEFT, padx=5)

        # 音频信息显示区域
        info_frame = ttk.LabelFrame(main_frame, text="音频文件信息", padding="5")
        info_frame.pack(fill=tk.X, pady=5)

        self.info_text = tk.Text(info_frame, height=6, width=50, wrap=tk.WORD)
        self.info_text.pack(fill=tk.X, padx=5, pady=5)
        self.info_text.config(state=tk.DISABLED)

        # 处理方式选择区域
        param_frame = ttk.LabelFrame(main_frame, text="2. 处理方式", padding="5")
        param_frame.pack(fill=tk.X, pady=5)

        # 处理方式选择
        norm_type_frame = ttk.Frame(param_frame)
        norm_type_frame.pack(fill=tk.X, pady=5)
        ttk.Label(norm_type_frame, text="处理方式:").pack(side=tk.LEFT, padx=5)
        self.norm_type = tk.StringVar(value="不处理")
        ttk.Radiobutton(norm_type_frame, text="不处理", variable=self.norm_type, 
                       value="不处理", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(norm_type_frame, text="只保留人声", variable=self.norm_type, 
                       value="只保留人声", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(norm_type_frame, text="只保留人声6s", variable=self.norm_type, 
                       value="只保留人声6s", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(norm_type_frame, text="人声修复", variable=self.norm_type, 
                       value="人声修复", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(norm_type_frame, text="提取人声 AudioSx", variable=self.norm_type,
                       value="提取人声 AudioSx", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(norm_type_frame, text="提取人声 UVR5", variable=self.norm_type,
                       value="提取人声 UVR5", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)

        # 新增第二行选项
        norm_type_frame2 = ttk.Frame(param_frame)
        norm_type_frame2.pack(fill=tk.X, pady=5)
        ttk.Radiobutton(norm_type_frame2, text="提取人声 MDX-Net", variable=self.norm_type,
                       value="提取人声 MDX-Net", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(norm_type_frame2, text="提取人声 MDX23C", variable=self.norm_type,
                       value="提取人声 MDX23C", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)

        # MDX-Net模型选择
        self.mdx_model_frame = ttk.LabelFrame(param_frame, text="MDX-Net 模型选择")
        self.mdx_model_var = tk.StringVar(value="UVR-MDX-NET-Voc_FT")

        mdx_models = [
            ("UVR-MDX-NET-Voc_FT", "人声提取 (推荐)"),
            ("UVR-MDX-NET-Inst_HQ_5", "乐器分离 HQ5"),
            ("UVR-MDX-NET-Inst_HQ_4", "乐器分离 HQ4"),
            ("UVR-MDX-NET-Inst_HQ_3", "乐器分离 HQ3"),
        ]

        for i, (model_id, model_desc) in enumerate(mdx_models):
            ttk.Radiobutton(self.mdx_model_frame, text=model_desc, variable=self.mdx_model_var,
                           value=model_id).pack(anchor=tk.W, padx=5, pady=2)

        # 目标值设置
        self.target_frame = ttk.Frame(param_frame)
        self.target_frame.pack(fill=tk.X, pady=5)
        self.target_label = ttk.Label(self.target_frame, text="目标dBFS:")
        self.target_label.pack(side=tk.LEFT, padx=5)
        self.target_value = tk.StringVar(value="-0.1")
        self.target_entry = ttk.Entry(self.target_frame, textvariable=self.target_value, width=10)
        self.target_entry.pack(side=tk.LEFT, padx=5)

        # 输出格式选择
        format_frame = ttk.Frame(param_frame)
        format_frame.pack(fill=tk.X, pady=5)
        ttk.Label(format_frame, text="输出格式:").pack(side=tk.LEFT, padx=5)
        self.output_format = tk.StringVar(value="wav")
        ttk.Radiobutton(format_frame, text="WAV", variable=self.output_format, 
                       value="wav").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(format_frame, text="FLAC", variable=self.output_format, 
                       value="flac").pack(side=tk.LEFT, padx=5)

        # 处理按钮
        process_frame = ttk.Frame(main_frame)
        process_frame.pack(fill=tk.X, pady=10)
        self.process_button = ttk.Button(process_frame, text="处理音频", 
                                       command=self.process_audio, state=tk.DISABLED)
        self.process_button.pack(pady=5)

        # 状态显示
        self.status_var = tk.StringVar(value="状态: 就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.pack(side=tk.BOTTOM, fill=tk.X, pady=5)

    def update_audio_info(self, file_path):
        """更新音频文件信息显示"""
        try:
            # 读取音频文件
            data, sr = sf.read(file_path)

            # 计算峰值电平 (dBFS)
            peak_linear = np.max(np.abs(data))
            peak_dbfs = 20 * np.log10(peak_linear) if peak_linear > 0 else -np.inf
            
            # 计算 LUFS
            meter = pyln.Meter(sr)
            lufs = meter.integrated_loudness(data)
            
            # 获取音频时长
            duration = len(data) / sr
            
            # 获取声道数
            channels = 1 if len(data.shape) == 1 else data.shape[1]
            
            # 获取采样率
            sample_rate = sr
            
            # 获取位深度
            bit_depth = sf.info(file_path).subtype
            
            # 更新信息显示
            info_text = f"""文件信息:
采样率: {sample_rate} Hz
位深度: {bit_depth}
声道数: {channels}
时长: {duration:.2f} 秒
峰值电平: {peak_dbfs:.2f} dBFS
响度: {lufs:.2f} LUFS"""
            
            self.info_text.config(state=tk.NORMAL)
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(tk.END, info_text)
            self.info_text.config(state=tk.DISABLED)
            
        except Exception as e:
            self.info_text.config(state=tk.NORMAL)
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(tk.END, f"无法读取音频信息：{str(e)}")
            self.info_text.config(state=tk.DISABLED)

    def update_norm_params(self):
        # 隐藏所有可选参数框
        self.target_frame.pack_forget()
        self.mdx_model_frame.pack_forget()

        if self.norm_type.get() == "不处理":
            pass
        elif self.norm_type.get() == "只保留人声":
            self.target_label.config(text="目标dBFS:")
            self.target_value.set("-0.1")
            self.target_frame.pack(fill=tk.X, pady=5)
        elif self.norm_type.get() == "只保留人声6s":
            self.target_label.config(text="目标dBFS:")
            self.target_value.set("-0.1")
            self.target_frame.pack(fill=tk.X, pady=5)
        elif self.norm_type.get() == "人声修复":
            pass
        elif self.norm_type.get() == "提取人声 AudioSx":
            pass
        elif self.norm_type.get() == "提取人声 UVR5":
            pass
        elif self.norm_type.get() == "提取人声 MDX-Net":
            # 显示MDX-Net模型选择
            self.mdx_model_frame.pack(fill=tk.X, pady=5)
        elif self.norm_type.get() == "提取人声 MDX23C":
            pass

    def browse_file(self):
        filetypes = (
            ("音频文件", "*.wav *.flac *.mp3 *.aac"),
            ("所有文件", "*.*")
        )
        filename = filedialog.askopenfilename(
            title="选择音频文件",
            filetypes=filetypes
        )
        if filename:
            self.file_path.set(filename)
            self.process_button.config(state=tk.NORMAL)
            # 更新音频信息显示
            self.update_audio_info(filename)

    def process_audio(self):
        input_file = self.file_path.get()
        if not input_file:
            messagebox.showwarning("警告", "请先选择音频文件")
            return

        try:
            if self.norm_type.get() not in ["只保留人声", "只保留人声6s", "人声修复", "提取人声 AudioSx", "提取人声 UVR5", "提取人声 MDX-Net", "提取人声 MDX23C"]:
                target_value = float(self.target_value.get())
        except ValueError:
            messagebox.showerror("错误", "目标值必须是有效的数字")
            return

        self.status_var.set("状态: 正在处理音频...")
        self.master.update_idletasks()

        try:
            # 读取音频文件
            print(f"读取音频文件: {input_file}")
            data, sr = sf.read(input_file)

            # 根据选择的处理方式处理音频
            if self.norm_type.get() == "不处理":
                processed_audio = data
                target_str = "不处理"
            elif self.norm_type.get() == "只保留人声":
                processed_audio = peak_normalize_audio_single_param(data, target_dbfs=target_value)
                target_str = f"{target_value}dBFS"
            elif self.norm_type.get() == "只保留人声6s":
                processed_audio = extract_vocals_demucs_6s(data, sr)
                target_str = "vocals_6s"
            elif self.norm_type.get() == "人声修复":
                processed_audio = fix_vocals_voicefixer(data, sr)
                target_str = "fix_vocals"
            elif self.norm_type.get() == "提取人声 AudioSx":
                processed_audio = extract_vocals_audio_sx(data, sr)
                target_str = "vocals_AudioSx"
            elif self.norm_type.get() == "提取人声 UVR5":
                processed_audio = extract_vocals_uvr5(data, sr)
                target_str = "vocals_UVR5"
            elif self.norm_type.get() == "提取人声 MDX-Net":
                selected_model = self.mdx_model_var.get()
                processed_audio = extract_vocals_mdx_net(data, sr, model_name=selected_model)
                target_str = f"vocals_MDX_Net_{selected_model.replace('-', '_')}"
            elif self.norm_type.get() == "提取人声 MDX23C":
                processed_audio = extract_vocals_mdx23c(data, sr)
                target_str = "vocals_MDX23C"

            # 生成输出文件名
            base_name = os.path.splitext(input_file)[0]
            output_format = self.output_format.get()
            output_file = f"{base_name}_{target_str}.{output_format}"

            # 保存处理后的音频
            print(f"保存处理后的音频到: {output_file}")
            # 确保音频数据是float32类型
            processed_audio = processed_audio.astype(np.float32)
            # 确保音频数据在[-1, 1]范围内
            processed_audio = np.clip(processed_audio, -1.0, 1.0)
            # 去除NaN和Inf
            if np.isnan(processed_audio).any() or np.isinf(processed_audio).any():
                print("警告：音频数据包含NaN或Inf，将其置零！")
                processed_audio = np.nan_to_num(processed_audio, nan=0.0, posinf=0.0, neginf=0.0)
            # 如果是一维（单通道），扩展为双通道
            if len(processed_audio.shape) == 1:
                processed_audio = np.stack([processed_audio, processed_audio], axis=-1)
            elif processed_audio.shape[1] == 1:
                processed_audio = np.repeat(processed_audio, 2, axis=1)
            # 打印调试信息
            print(f"音频数据 shape: {processed_audio.shape}, dtype: {processed_audio.dtype}, min: {processed_audio.min()}, max: {processed_audio.max()}")
            # 转换为int16
            processed_audio_int16 = (processed_audio * 32767.0).astype(np.int16)
            # 修正shape为(samples, channels)
            if processed_audio_int16.shape[0] == 2 and processed_audio_int16.shape[1] > 2:
                processed_audio_int16 = processed_audio_int16.T
            print(f"转换后 shape: {processed_audio_int16.shape}, dtype: {processed_audio_int16.dtype}, min: {processed_audio_int16.min()}, max: {processed_audio_int16.max()}")
            # 保存为PCM_16格式
            sf.write(output_file, processed_audio_int16, sr, subtype='PCM_16')

            # 显示成功消息
            messagebox.showinfo("成功", f"音频处理完成！\n已保存至：\n{output_file}")
            self.status_var.set("状态: 处理完成")

        except Exception as e:
            error_msg = f"处理音频时出错：\n{str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)
            self.status_var.set("状态: 处理失败")

if __name__ == "__main__":
    root = tk.Tk()
    app = AudioNormalizerGUI(root)
    root.mainloop()
# UVR5 VR architecture人声提取
def extract_vocals_uvr5(audio_data, sample_rate):
    """
    使用 UVR5 的 VR architecture 模型提取人声。
    这是一个独立的实现，使用频谱减法技术。

    参数:
    audio_data (np.ndarray): 音频数据 (samples, channels) 或 (samples,)
    sample_rate (int): 采样率

    返回:
    np.ndarray: 提取的人声数据
    """
    print("开始使用 UVR5 频谱减法算法提取人声...")

    try:
        # 确保音频数据格式正确
        if len(audio_data.shape) == 1:
            audio_data = np.stack([audio_data, audio_data], axis=1)
        elif audio_data.shape[1] == 1:
            audio_data = np.repeat(audio_data, 2, axis=1)

        print("执行频谱减法人声提取...")

        # 使用频谱减法技术提取人声
        vocals = spectral_subtraction_vocal_extraction(audio_data, sample_rate)

        print("UVR5 频谱减法人声提取完成")
        return vocals

    except Exception as e:
        print(f"使用 UVR5 提取人声时出错: {str(e)}")
        raise

def spectral_subtraction_vocal_extraction(audio_data, sample_rate):
    """
    使用频谱减法技术提取人声
    """
    left_channel = audio_data[:, 0]
    right_channel = audio_data[:, 1]

    # 参数设置
    n_fft = 2048
    hop_length = 512

    # 对左右声道进行STFT
    stft_left = librosa.stft(left_channel, n_fft=n_fft, hop_length=hop_length)
    stft_right = librosa.stft(right_channel, n_fft=n_fft, hop_length=hop_length)

    # 计算幅度和相位
    mag_left = np.abs(stft_left)
    mag_right = np.abs(stft_right)
    phase_left = np.angle(stft_left)

    # 频谱减法：假设人声在中心，伴奏在两侧
    # 计算中心信号（人声）和侧信号（伴奏）
    center_mag = (mag_left + mag_right) / 2
    side_mag = np.abs(mag_left - mag_right) / 2

    # 增强人声，抑制伴奏
    alpha = 0.7  # 人声增强因子
    beta = 0.3   # 伴奏抑制因子

    # 计算人声掩码
    vocal_mask = center_mag / (center_mag + side_mag + 1e-10)
    vocal_mask = np.clip(vocal_mask * alpha, 0, 1)

    # 应用掩码提取人声
    vocal_mag = center_mag * vocal_mask

    # 重建复数频谱
    vocal_stft = vocal_mag * np.exp(1j * phase_left)

    # 逆STFT重建时域信号
    vocals = librosa.istft(vocal_stft, hop_length=hop_length)

    # 应用后处理滤波器
    vocals = apply_vocal_enhancement_filter(vocals, sample_rate)

    return vocals.reshape(-1, 1)

def apply_vocal_enhancement_filter(audio, sample_rate):
    """
    应用人声增强滤波器
    """
    try:
        from scipy import signal

        # 设计带通滤波器，突出人声频率范围 (80Hz - 8000Hz)
        nyquist = sample_rate / 2
        low_cutoff = 80 / nyquist
        high_cutoff = min(8000 / nyquist, 0.95)  # 确保不超过奈奎斯特频率

        if low_cutoff < high_cutoff and high_cutoff < 1.0:
            b, a = signal.butter(4, [low_cutoff, high_cutoff], btype='band')
            audio = signal.filtfilt(b, a, audio)

        # 应用轻微的压缩来平衡动态范围
        threshold = 0.7
        ratio = 3.0
        audio_abs = np.abs(audio)
        mask = audio_abs > threshold
        audio[mask] = np.sign(audio[mask]) * (threshold + (audio_abs[mask] - threshold) / ratio)

    except ImportError:
        print("scipy不可用，跳过滤波器处理")
    except Exception as e:
        print(f"滤波器处理出错: {e}")

    return audio