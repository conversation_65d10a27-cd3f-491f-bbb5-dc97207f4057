import numpy as np
import soundfile as sf # 推荐使用 soundfile
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import pyloudnorm as pyln
import torch
import torchaudio
from demucs.pretrained import get_model
from demucs.apply import apply_model
import tempfile
import shutil
# from moviepy.editor import VideoFileClip # 导入 moviepy

def peak_normalize_audio_single_param(audio_data, target_dbfs=-0.1):
    """
    对音频数据进行峰值归一化，使用单一目标峰值参数。

    参数:
    audio_data (np.ndarray): 音频数据。期望是浮点数 [-1.0, 1.0] 或会自动转换。
                             如果是多声道，期望形状为 (n_samples, n_channels)。
    target_dbfs (float): 目标峰值电平，单位 dBFS。
                         默认值为 -0.1 dBFS，这是一个推荐的"最佳配置"。

    返回:
    np.ndarray: 归一化后的音频数据，数据类型与输入尽可能保持一致（优先浮点）。
    """

    # 1. 确定数据类型并转换为浮点数 [-1.0, 1.0]
    #    这个逻辑可以更健壮，参考上一个答案的详细版本
    #    这里简化假设，如果不是float，则尝试转换
    original_dtype = audio_data.dtype
    is_integer_input = np.issubdtype(original_dtype, np.integer)

    if is_integer_input:
        if original_dtype == np.int16:
            audio_float = audio_data.astype(np.float32) / 32768.0
        elif original_dtype == np.int32: # 假设是全范围的int32
            audio_float = audio_data.astype(np.float32) / (2**31 -1) # 2147483647.0
        elif original_dtype == np.uint8:
            audio_float = (audio_data.astype(np.float32) - 128.0) / 128.0
        else: # 其他整数类型，或需要更精确的位数信息
            print(f"Warning: Integer type {original_dtype} might not be scaled correctly. Prefer float input or use a more robust conversion.")
            # 尝试一个通用（但不完美）的转换
            iinfo = np.iinfo(original_dtype)
            audio_float = audio_data.astype(np.float32) / float(max(abs(iinfo.min), iinfo.max))
    elif np.issubdtype(original_dtype, np.floating):
        audio_float = audio_data.astype(np.float32) # 确保是 float32 进行计算
    else:
        raise ValueError(f"Unsupported audio_data dtype: {original_dtype}")

    # 2. 找到当前峰值 (线性)
    current_peak_linear = np.max(np.abs(audio_float))

    # 3. 处理静音或接近静音的情况
    if current_peak_linear < 1e-9: # 阈值可以调整
        # print("Audio is silent or near silent, no normalization applied.")
        if is_integer_input:
            return np.zeros_like(audio_data, dtype=original_dtype)
        return audio_float 

    # 4. 计算目标线性峰值
    target_peak_linear = 10**(target_dbfs / 20.0)

    # 5. 计算增益因子
    gain = target_peak_linear / current_peak_linear
    
    # 6. 应用增益
    normalized_audio_float = audio_float * gain

    # 7. (可选但推荐) 削波保护，确保严格在 [-1.0, 1.0] 内
    normalized_audio_float = np.clip(normalized_audio_float, -1.0, 1.0)

    # 8. 转换回原始整数类型 (如果输入是整数)
    if is_integer_input:
        if original_dtype == np.int16:
            final_audio = np.round(normalized_audio_float * 32767.0).astype(np.int16)
        elif original_dtype == np.int32:
            final_audio = np.round(normalized_audio_float * (2**31 - 1)).astype(np.int32)
        elif original_dtype == np.uint8:
            final_audio = np.round((normalized_audio_float * 127.0) + 128.0).astype(np.uint8)
            final_audio = np.clip(final_audio, 0, 255)
        else:
            # 对于其他未明确处理的整数类型，返回浮点数
            print(f"Warning: Returning float32 for unhandled original integer dtype {original_dtype}")
            return normalized_audio_float.astype(np.float32)
        return final_audio
    else:
        # 如果输入是浮点数，直接返回处理后的浮点数 (保持原始浮点类型 float32/float64)
        return normalized_audio_float.astype(original_dtype)

def lufs_normalize_audio(audio_data, sample_rate, target_lufs=-16.0):
    """
    对音频数据进行 LUFS 归一化。

    参数:
    audio_data (np.ndarray): 音频数据。期望是浮点数 [-1.0, 1.0] 或会自动转换。
                             如果是多声道，期望形状为 (n_samples, n_channels)。
    sample_rate (int): 采样率
    target_lufs (float): 目标 LUFS 值，默认为 -16.0 LUFS

    返回:
    np.ndarray: 归一化后的音频数据
    """
    # 确保音频数据是浮点数
    if not np.issubdtype(audio_data.dtype, np.floating):
        audio_data = audio_data.astype(np.float32)
        if np.issubdtype(audio_data.dtype, np.integer):
            if audio_data.dtype == np.int16:
                audio_data = audio_data / 32768.0
            elif audio_data.dtype == np.int32:
                audio_data = audio_data / (2**31 - 1)
            elif audio_data.dtype == np.uint8:
                audio_data = (audio_data - 128.0) / 128.0

    # 创建 LUFS 计量器
    meter = pyln.Meter(sample_rate)
    
    # 计算当前 LUFS 值
    current_lufs = meter.integrated_loudness(audio_data)
    
    # 计算增益
    gain_db = target_lufs - current_lufs
    gain_linear = 10 ** (gain_db / 20.0)
    
    # 应用增益
    normalized_audio = audio_data * gain_linear
    
    # 确保不削波
    normalized_audio = np.clip(normalized_audio, -1.0, 1.0)
    
    return normalized_audio

def remove_background_music(audio_data, sample_rate, model_name='htdemucs', stem_index=None):
    """
    使用 Demucs 分离音频，保留人声和音效，去除背景音乐。

    参数:
    audio_data (np.ndarray): 音频数据 (samples, channels) 或 (samples,)
    sample_rate (int): 采样率
    model_name (str): Demucs 模型名称
    stem_index (int): 要提取的音轨索引，None表示提取所有音轨

    返回:
    np.ndarray: 处理后的音频数据，形状 (samples, channels) 或 (samples,)
    """
    print("开始处理音频...")
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    try:
        # Demucs 通常处理 float32 格式
        audio_data_float32 = audio_data.astype(np.float32)

        # 保存临时音频文件
        temp_input = os.path.join(temp_dir, 'temp_input.wav')
        print(f"保存临时文件到: {temp_input}")
        sf.write(temp_input, audio_data_float32, sample_rate)

        # 创建输出目录
        output_dir = os.path.join(temp_dir, 'output')
        os.makedirs(output_dir, exist_ok=True)

        # 使用 demucs.separate.main 分离音频
        print("开始分离音频...")
        import demucs.separate
        demucs.separate.main([temp_input, '-o', output_dir, '-n', model_name])
        
        # 获取分离后的文件路径
        separated_dir = os.path.join(output_dir, model_name, 'temp_input')
        
        # 根据 stem_index 选择要读取的文件
        if stem_index is not None:
            stem_names = ['vocals', 'drums', 'bass', 'other']
            stem_name = stem_names[stem_index]
            stem_path = os.path.join(separated_dir, f'{stem_name}.wav')
            print(f"读取音轨: {stem_name}")
            final_audio_data, _ = sf.read(stem_path)
        else:
            # 合并人声和其他音效
            vocals_path = os.path.join(separated_dir, 'vocals.wav')
            other_path = os.path.join(separated_dir, 'other.wav')
            vocals_data, _ = sf.read(vocals_path)
            other_data, _ = sf.read(other_path)
            final_audio_data = vocals_data + other_data
            print("合并人声和其他音效")
            
        return final_audio_data

    except Exception as e:
        print(f"处理音频时出错: {str(e)}")
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        raise
    finally:
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

class AudioNormalizerGUI:
    def __init__(self, master):
        self.master = master
        master.title("音频处理工具")
        master.geometry("600x550")  # 增加窗口高度

        # 创建主框架
        main_frame = ttk.Frame(master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="1. 选择音频文件", padding="5")
        file_frame.pack(fill=tk.X, pady=5)

        self.file_path = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file_path, width=50).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_frame, text="浏览", command=self.browse_file).pack(side=tk.LEFT, padx=5)

        # 音频信息显示区域
        info_frame = ttk.LabelFrame(main_frame, text="音频文件信息", padding="5")
        info_frame.pack(fill=tk.X, pady=5)

        self.info_text = tk.Text(info_frame, height=6, width=50, wrap=tk.WORD)
        self.info_text.pack(fill=tk.X, padx=5, pady=5)
        self.info_text.config(state=tk.DISABLED)

        # 处理方式选择区域
        param_frame = ttk.LabelFrame(main_frame, text="2. 处理方式", padding="5")
        param_frame.pack(fill=tk.X, pady=5)

        # 处理方式选择
        norm_type_frame = ttk.Frame(param_frame)
        norm_type_frame.pack(fill=tk.X, pady=5)
        ttk.Label(norm_type_frame, text="处理方式:").pack(side=tk.LEFT, padx=5)
        self.norm_type = tk.StringVar(value="peak")
        ttk.Radiobutton(norm_type_frame, text="峰值归一", variable=self.norm_type, 
                       value="peak", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(norm_type_frame, text="LUFS归一", variable=self.norm_type, 
                       value="lufs", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(norm_type_frame, text="去背景音乐", variable=self.norm_type, 
                       value="remove_bgm", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(norm_type_frame, text="去背景音乐ft", variable=self.norm_type, 
                       value="remove_bgm_ft", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(norm_type_frame, text="去背景音乐6s", variable=self.norm_type, 
                       value="remove_bgm_6s", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(norm_type_frame, text="只留人声", variable=self.norm_type, 
                       value="vocals_only", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(norm_type_frame, text="只留鼓点", variable=self.norm_type, 
                       value="drums_only", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(norm_type_frame, text="只留低音", variable=self.norm_type, 
                       value="bass_only", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(norm_type_frame, text="只留其他6s", variable=self.norm_type, 
                       value="other_only", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(norm_type_frame, text="提取人声6s", variable=self.norm_type, 
                       value="vocals_6s", command=self.update_norm_params).pack(side=tk.LEFT, padx=5)

        # 目标值设置
        self.target_frame = ttk.Frame(param_frame)
        self.target_frame.pack(fill=tk.X, pady=5)
        self.target_label = ttk.Label(self.target_frame, text="目标dBFS:")
        self.target_label.pack(side=tk.LEFT, padx=5)
        self.target_value = tk.StringVar(value="-0.1")
        self.target_entry = ttk.Entry(self.target_frame, textvariable=self.target_value, width=10)
        self.target_entry.pack(side=tk.LEFT, padx=5)

        # 输出格式选择
        format_frame = ttk.Frame(param_frame)
        format_frame.pack(fill=tk.X, pady=5)
        ttk.Label(format_frame, text="输出格式:").pack(side=tk.LEFT, padx=5)
        self.output_format = tk.StringVar(value="wav")
        ttk.Radiobutton(format_frame, text="WAV", variable=self.output_format, 
                       value="wav").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(format_frame, text="FLAC", variable=self.output_format, 
                       value="flac").pack(side=tk.LEFT, padx=5)

        # 处理按钮
        process_frame = ttk.Frame(main_frame)
        process_frame.pack(fill=tk.X, pady=10)
        self.process_button = ttk.Button(process_frame, text="处理音频", 
                                       command=self.process_audio, state=tk.DISABLED)
        self.process_button.pack(pady=5)

        # 状态显示
        self.status_var = tk.StringVar(value="状态: 就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.pack(side=tk.BOTTOM, fill=tk.X, pady=5)

    def update_audio_info(self, file_path):
        """更新音频文件信息显示"""
        try:
            # 读取音频文件
            data, sr = sf.read(file_path)

            # 计算峰值电平 (dBFS)
            peak_linear = np.max(np.abs(data))
            peak_dbfs = 20 * np.log10(peak_linear) if peak_linear > 0 else -np.inf
            
            # 计算 LUFS
            meter = pyln.Meter(sr)
            lufs = meter.integrated_loudness(data)
            
            # 获取音频时长
            duration = len(data) / sr
            
            # 获取声道数
            channels = 1 if len(data.shape) == 1 else data.shape[1]
            
            # 获取采样率
            sample_rate = sr
            
            # 获取位深度
            bit_depth = sf.info(file_path).subtype
            
            # 更新信息显示
            info_text = f"""文件信息:
采样率: {sample_rate} Hz
位深度: {bit_depth}
声道数: {channels}
时长: {duration:.2f} 秒
峰值电平: {peak_dbfs:.2f} dBFS
响度: {lufs:.2f} LUFS"""
            
            self.info_text.config(state=tk.NORMAL)
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(tk.END, info_text)
            self.info_text.config(state=tk.DISABLED)
            
        except Exception as e:
            self.info_text.config(state=tk.NORMAL)
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(tk.END, f"无法读取音频信息：{str(e)}")
            self.info_text.config(state=tk.DISABLED)

    def update_norm_params(self):
        if self.norm_type.get() == "peak":
            self.target_label.config(text="目标dBFS:")
            self.target_value.set("-0.1")
            self.target_frame.pack(fill=tk.X, pady=5)
        elif self.norm_type.get() == "lufs":
            self.target_label.config(text="目标LUFS:")
            self.target_value.set("-16.0")
            self.target_frame.pack(fill=tk.X, pady=5)
        elif self.norm_type.get() == "remove_bgm":
            self.target_frame.pack_forget()
        elif self.norm_type.get() == "remove_bgm_ft":
            self.target_frame.pack_forget()
        elif self.norm_type.get() == "remove_bgm_6s":
            self.target_frame.pack_forget()
        elif self.norm_type.get() in ["vocals_only", "drums_only", "bass_only", "other_only", "vocals_6s"]:
            self.target_frame.pack_forget()

    def browse_file(self):
        filetypes = (
            ("音频文件", "*.wav *.flac *.mp3 *.aac"),
            ("所有文件", "*.*")
        )
        filename = filedialog.askopenfilename(
            title="选择音频文件",
            filetypes=filetypes
        )
        if filename:
            self.file_path.set(filename)
            self.process_button.config(state=tk.NORMAL)
            # 更新音频信息显示
            self.update_audio_info(filename)

    def process_audio(self):
        input_file = self.file_path.get()
        if not input_file:
            messagebox.showwarning("警告", "请先选择音频文件")
            return

        try:
            if self.norm_type.get() != "remove_bgm":
                target_value = float(self.target_value.get())
        except ValueError:
            messagebox.showerror("错误", "目标值必须是有效的数字")
            return

        self.status_var.set("状态: 正在处理音频...")
        self.master.update_idletasks()

        try:
            # 读取音频文件
            print(f"读取音频文件: {input_file}")
            # sf.read 默认返回 float64, 形状 (samples, channels) 或 (samples,)
            data, sr = sf.read(input_file)

            # 根据选择的处理方式处理音频
            if self.norm_type.get() == "peak":
                # 峰值归一化可以直接处理单声道或双声道，保持原始声道数
                processed_audio = peak_normalize_audio_single_param(data, target_dbfs=target_value)
                target_str = f"{target_value}dBFS"
            elif self.norm_type.get() == "lufs":
                 # LUFS归一化通常也处理多声道，保持原始声道数
                processed_audio = lufs_normalize_audio(data, sr, target_lufs=target_value)
                target_str = f"{target_value}LUFS"
            elif self.norm_type.get() == "remove_bgm":
                print("开始去除背景音乐...")
                processed_audio = remove_background_music(data, sr, model_name='htdemucs', stem_index=None)
                target_str = "no_bgm"
            elif self.norm_type.get() == "remove_bgm_ft":
                print("开始去除背景音乐 (htdemucs_ft)...")
                processed_audio = remove_background_music(data, sr, model_name='htdemucs_ft', stem_index=None)
                target_str = "no_bgm_ft"
            elif self.norm_type.get() == "remove_bgm_6s":
                print("开始去除背景音乐 (htdemucs_6s)...")
                processed_audio = remove_background_music(data, sr, model_name='htdemucs_6s', stem_index=None)
                target_str = "no_bgm_6s"
            elif self.norm_type.get() == "vocals_only":
                print("开始提取人声...")
                processed_audio = remove_background_music(data, sr, model_name='htdemucs_ft', stem_index=0)
                target_str = "vocals_only"
            elif self.norm_type.get() == "drums_only":
                print("开始提取鼓点...")
                processed_audio = remove_background_music(data, sr, model_name='htdemucs_ft', stem_index=1)
                target_str = "drums_only"
            elif self.norm_type.get() == "bass_only":
                print("开始提取低音...")
                processed_audio = remove_background_music(data, sr, model_name='htdemucs_ft', stem_index=2)
                target_str = "bass_only"
            elif self.norm_type.get() == "other_only":
                print("开始提取其他音轨...")
                processed_audio = remove_background_music(data, sr, model_name='htdemucs_6s', stem_index=3)
                target_str = "other_only"
            elif self.norm_type.get() == "vocals_6s":
                print("开始提取人声6s...")
                processed_audio = remove_background_music(data, sr, model_name='htdemucs_6s', stem_index=0)
                target_str = "vocals_6s"

            # 生成输出文件名
            base_name = os.path.splitext(input_file)[0]
            output_format = self.output_format.get()
            output_file = f"{base_name}_{target_str}.{output_format}"

            # 保存处理后的音频
            print(f"保存处理后的音频到: {output_file}")
            # processed_audio 现在已经是 (samples, channels) 或 (samples,) 形状
            sf.write(output_file, processed_audio, sr)

            # 显示成功消息
            messagebox.showinfo("成功", f"音频处理完成！\n已保存至：\n{output_file}")
            self.status_var.set("状态: 处理完成")

        except Exception as e:
            error_msg = f"处理音频时出错：\n{str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)
            self.status_var.set("状态: 处理失败")

if __name__ == "__main__":
    root = tk.Tk()
    app = AudioNormalizerGUI(root)
    root.mainloop()