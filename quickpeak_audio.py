import os
os.environ['OBJC_DISABLE_INITIALIZE_FORK_SAFETY'] = 'YES'

import customtkinter as ctk
from tkinter import filedialog, messagebox
import numpy as np
import soundfile as sf
import os
import threading
import requests
from urllib.parse import urlparse
import tempfile
from pathlib import Path
import concurrent.futures
import time
import pandas as pd

def get_audio_dbfs(audio_data):
    """
    计算音频数据的dBFS值
    
    参数:
    audio_data (np.ndarray): 音频数据，期望是浮点数 [-1.0, 1.0]
    
    返回:
    float: 音频的dBFS值
    """
    # 确保数据是浮点数
    if np.issubdtype(audio_data.dtype, np.integer):
        if audio_data.dtype == np.int16:
            audio_float = audio_data.astype(np.float32) / 32768.0
        elif audio_data.dtype == np.int32:
            audio_float = audio_data.astype(np.float32) / (2**31 - 1)
        elif audio_data.dtype == np.uint8:
            audio_float = (audio_data.astype(np.float32) - 128.0) / 128.0
        else:
            iinfo = np.iinfo(audio_data.dtype)
            audio_float = audio_data.astype(np.float32) / float(max(abs(iinfo.min), iinfo.max))
    else:
        audio_float = audio_data.astype(np.float32)
    
    # 计算峰值
    peak = np.max(np.abs(audio_float))
    if peak < 1e-9:  # 处理静音情况
        return -float('inf')
    
    # 转换为dBFS
    dbfs = 20 * np.log10(peak)
    return dbfs

def peak_normalize_audio(audio_data, target_dbfs=-0.1):
    """
    对音频数据进行峰值归一化，使用单一目标峰值参数。

    参数:
    audio_data (np.ndarray): 音频数据。期望是浮点数 [-1.0, 1.0] 或会自动转换。
                             如果是多声道，期望形状为 (n_samples, n_channels)。
    target_dbfs (float): 目标峰值电平，单位 dBFS。
                         默认值为 -0.1 dBFS，这是一个推荐的"最佳配置"。

    返回:
    np.ndarray: 归一化后的音频数据，数据类型与输入尽可能保持一致（优先浮点）。
    """
    # 1. 确定数据类型并转换为浮点数 [-1.0, 1.0]
    original_dtype = audio_data.dtype
    is_integer_input = np.issubdtype(original_dtype, np.integer)

    if is_integer_input:
        if original_dtype == np.int16:
            audio_float = audio_data.astype(np.float32) / 32768.0
        elif original_dtype == np.int32: # 假设是全范围的int32
            audio_float = audio_data.astype(np.float32) / (2**31 -1) # 2147483647.0
        elif original_dtype == np.uint8:
            audio_float = (audio_data.astype(np.float32) - 128.0) / 128.0
        else: # 其他整数类型，或需要更精确的位数信息
            print(f"Warning: Integer type {original_dtype} might not be scaled correctly. Prefer float input or use a more robust conversion.")
            # 尝试一个通用（但不完美）的转换
            iinfo = np.iinfo(original_dtype)
            audio_float = audio_data.astype(np.float32) / float(max(abs(iinfo.min), iinfo.max))
    elif np.issubdtype(original_dtype, np.floating):
        audio_float = audio_data.astype(np.float32) # 确保是 float32 进行计算
    else:
        raise ValueError(f"Unsupported audio_data dtype: {original_dtype}")

    # 2. 找到当前峰值 (线性)
    current_peak_linear = np.max(np.abs(audio_float))

    # 3. 处理静音或接近静音的情况
    if current_peak_linear < 1e-9: # 阈值可以调整
        # print("Audio is silent or near silent, no normalization applied.")
        if is_integer_input:
            return np.zeros_like(audio_data, dtype=original_dtype)
        return audio_float 

    # 4. 计算目标线性峰值
    target_peak_linear = 10**(target_dbfs / 20.0)

    # 5. 计算增益因子
    gain = target_peak_linear / current_peak_linear
    
    # 6. 应用增益
    normalized_audio_float = audio_float * gain

    # 7. (可选但推荐) 削波保护，确保严格在 [-1.0, 1.0] 内
    normalized_audio_float = np.clip(normalized_audio_float, -1.0, 1.0)

    # 8. 转换回原始整数类型 (如果输入是整数)
    if is_integer_input:
        if original_dtype == np.int16:
            final_audio = np.round(normalized_audio_float * 32767.0).astype(np.int16)
        elif original_dtype == np.int32:
            final_audio = np.round(normalized_audio_float * (2**31 - 1)).astype(np.int32)
        elif original_dtype == np.uint8:
            final_audio = np.round((normalized_audio_float * 127.0) + 128.0).astype(np.uint8)
            final_audio = np.clip(final_audio, 0, 255)
        else:
            # 对于其他未明确处理的整数类型，返回浮点数
            print(f"Warning: Returning float32 for unhandled original integer dtype {original_dtype}")
            return normalized_audio_float.astype(np.float32)
        return final_audio
    else:
        # 如果输入是浮点数，直接返回处理后的浮点数 (保持原始浮点类型 float32/float64)
        return normalized_audio_float.astype(original_dtype)

def download_audio(url, temp_dir):
    """下载音频文件到临时目录"""
    try:
        print(f"开始下载: {url}")
        response = requests.get(url, stream=True)
        response.raise_for_status()

        # 尝试从URL中获取文件名和扩展名
        parsed_url = urlparse(url)
        filename_from_url = os.path.basename(parsed_url.path)
        file_extension = os.path.splitext(filename_from_url)[1].lower()

        # 如果无法从URL获取有效扩展名，使用通用临时扩展名
        if not file_extension or file_extension not in {'.wav', '.mp3', '.flac', '.aac'}:
            filename = f"audio_{int(time.time())}.temp"
        else:
            filename = filename_from_url
        
        temp_path = os.path.join(temp_dir, filename)
        print(f"写入文件路径: {temp_path}")
            

        # Ensure the directory exists before writing
        os.makedirs(os.path.dirname(temp_path), exist_ok=True)

        with open(temp_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        print(f"下载成功: {url} -> {temp_path}")
        return temp_path
    except Exception as e:
        print(f"下载失败: {url} - {str(e)}")
        raise Exception(f"下载失败: {str(e)}")

def process_audio_file(input_path, output_dir, target_dbfs):
    """处理单个音频文件"""
    try:
        print(f"开始处理文件: {input_path}")
        # 读取音频
        data, sr = sf.read(input_path)
        print(f"成功读取文件: {input_path}")
        print(f"音频格式: {data.dtype}, 采样率: {sr}, 长度: {len(data)}")

        # 归一化
        normalized_data = peak_normalize_audio(data, target_dbfs)

        # 生成输出文件名
        base_name = os.path.splitext(os.path.basename(input_path))[0]
        # 如果临时文件使用了.temp扩展名或没有扩展名，则输出为.wav，否则保留原扩展名
        original_extension = os.path.splitext(input_path)[1]
        if not original_extension or original_extension == '.temp':
            output_filename = f"{base_name}_normalized_{target_dbfs}dBFS.wav"
        else:
            output_filename = f"{base_name}_normalized_{target_dbfs}dBFS{original_extension}"

        output_path = os.path.join(output_dir, output_filename)

        # 保存
        sf.write(output_path, normalized_data, sr)
        print(f"处理并保存成功: {input_path} -> {output_path}")
        return True, output_path
    except Exception as e:
        print(f"处理文件失败: {input_path} - {str(e)}")
        return False, str(e)

class BatchAudioNormalizer(ctk.CTk):
    def __init__(self):
        super().__init__()
        
        self.title("批量音频归一化工具")
        self.geometry("800x600")
        ctk.set_appearance_mode("System")
        ctk.set_default_color_theme("blue")
        
        # 创建主框架
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.pack(pady=20, padx=20, fill="both", expand=True)
        
        # 输入方式选择
        self.input_mode = ctk.StringVar(value="files")
        self.create_input_mode_selector()
        
        # 输入区域
        self.create_input_area()
        
        # 参数设置
        self.create_parameter_area()
        
        # 进度显示
        self.create_progress_area()
        
        # 状态显示
        self.status_label = ctk.CTkLabel(self, text="就绪")
        self.status_label.pack(pady=10)
        
        # 按钮区域
        self.create_button_area()
        
        # 初始化变量
        self.processing = False
        self.temp_dir = None
        self.audio_info = []  # 存储音频信息

    def create_input_mode_selector(self):
        mode_frame = ctk.CTkFrame(self.main_frame)
        mode_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(mode_frame, text="选择输入方式:").pack(side="left", padx=5)
        
        ctk.CTkRadioButton(
            mode_frame,
            text="多文件选择",
            variable=self.input_mode,
            value="files",
            command=self.update_input_area
        ).pack(side="left", padx=5)
        
        ctk.CTkRadioButton(
            mode_frame,
            text="目录选择",
            variable=self.input_mode,
            value="directory",
            command=self.update_input_area
        ).pack(side="left", padx=5)
        
        ctk.CTkRadioButton(
            mode_frame,
            text="URL列表",
            variable=self.input_mode,
            value="urls",
            command=self.update_input_area
        ).pack(side="left", padx=5)

    def create_input_area(self):
        self.input_frame = ctk.CTkFrame(self.main_frame)
        self.input_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 文件选择区域
        self.files_frame = ctk.CTkFrame(self.input_frame)
        self.files_frame.pack(fill="both", expand=True)
        
        self.files_text = ctk.CTkTextbox(self.files_frame, height=100)
        self.files_text.pack(fill="both", expand=True, padx=5, pady=5)
        
        self.browse_button = ctk.CTkButton(
            self.files_frame,
            text="选择文件",
            command=self.browse_files
        )
        self.browse_button.pack(pady=5)
        
        # 目录选择区域
        self.directory_frame = ctk.CTkFrame(self.input_frame)
        self.directory_frame.pack(fill="both", expand=True)
        
        self.directory_entry = ctk.CTkEntry(self.directory_frame, width=400)
        self.directory_entry.pack(side="left", padx=5, pady=5)
        
        self.directory_button = ctk.CTkButton(
            self.directory_frame,
            text="选择目录",
            command=self.browse_directory
        )
        self.directory_button.pack(side="left", padx=5, pady=5)
        
        # URL输入区域
        self.urls_frame = ctk.CTkFrame(self.input_frame)
        self.urls_frame.pack(fill="both", expand=True)
        
        self.urls_text = ctk.CTkTextbox(self.urls_frame, height=100)
        self.urls_text.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 初始显示文件选择区域
        self.update_input_area()

    def create_parameter_area(self):
        param_frame = ctk.CTkFrame(self.main_frame)
        param_frame.pack(fill="x", padx=10, pady=5)
        
        # 目标dBFS设置
        ctk.CTkLabel(param_frame, text="目标dBFS:").pack(side="left", padx=5)
        self.target_dbfs = ctk.StringVar(value="-0.1")
        self.dbfs_entry = ctk.CTkEntry(param_frame, textvariable=self.target_dbfs, width=80)
        self.dbfs_entry.pack(side="left", padx=5)
        
        # 输出目录设置
        ctk.CTkLabel(param_frame, text="输出目录:").pack(side="left", padx=5)
        self.output_dir_entry = ctk.CTkEntry(param_frame, width=300)
        self.output_dir_entry.pack(side="left", padx=5)
        self.output_dir_entry.insert(0, os.path.expanduser("~/Desktop"))
        
        self.output_dir_button = ctk.CTkButton(
            param_frame,
            text="选择",
            command=self.browse_output_directory
        )
        self.output_dir_button.pack(side="left", padx=5)

    def create_progress_area(self):
        progress_frame = ctk.CTkFrame(self.main_frame)
        progress_frame.pack(fill="x", padx=10, pady=5)
        
        self.progress_bar = ctk.CTkProgressBar(progress_frame)
        self.progress_bar.pack(fill="x", padx=5, pady=5)
        self.progress_bar.set(0)
        
        self.progress_label = ctk.CTkLabel(progress_frame, text="0/0")
        self.progress_label.pack(pady=5)

    def create_button_area(self):
        button_frame = ctk.CTkFrame(self.main_frame)
        button_frame.pack(pady=10)
        
        self.process_button = ctk.CTkButton(
            button_frame,
            text="开始处理",
            command=self.start_processing
        )
        self.process_button.pack(side="left", padx=5)
        
        self.detect_button = ctk.CTkButton(
            button_frame,
            text="检测dBFS",
            command=self.start_detection
        )
        self.detect_button.pack(side="left", padx=5)

    def update_input_area(self):
        # 隐藏所有输入区域
        self.files_frame.pack_forget()
        self.directory_frame.pack_forget()
        self.urls_frame.pack_forget()
        
        # 显示选中的输入区域
        if self.input_mode.get() == "files":
            self.files_frame.pack(fill="both", expand=True)
        elif self.input_mode.get() == "directory":
            self.directory_frame.pack(fill="both", expand=True)
        else:  # urls
            self.urls_frame.pack(fill="both", expand=True)

    def browse_files(self):
        files = filedialog.askopenfilenames(
            title="选择音频文件",
            filetypes=(
                ("音频文件", "*.wav *.mp3 *.flac *.aac"),
                ("所有文件", "*.*")
            )
        )
        if files:
            self.files_text.delete("1.0", "end")
            self.files_text.insert("1.0", "\n".join(files))

    def browse_directory(self):
        directory = filedialog.askdirectory(title="选择音频文件目录")
        if directory:
            self.directory_entry.delete(0, "end")
            self.directory_entry.insert(0, directory)

    def browse_output_directory(self):
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_dir_entry.delete(0, "end")
            self.output_dir_entry.insert(0, directory)

    def get_input_files(self):
        mode = self.input_mode.get()
        if mode == "files":
            files = self.files_text.get("1.0", "end").strip().split("\n")
            return [f for f in files if f.strip()]
        elif mode == "directory":
            directory = self.directory_entry.get().strip()
            if not directory:
                return []
            return [
                str(p) for p in Path(directory).rglob("*")
                if p.suffix.lower() in {".wav", ".mp3", ".flac", ".aac"}
            ]
        else:  # urls
            urls = self.urls_text.get("1.0", "end").strip().split("\n")
            return [url for url in urls if url.strip()]

    def start_processing(self):
        if self.processing:
            return
            
        # 获取输入文件
        input_files = self.get_input_files()
        if not input_files:
            messagebox.showwarning("警告", "请先选择输入文件/目录/URL")
            return
            
        # 获取输出目录
        output_dir = self.output_dir_entry.get().strip()
        if not output_dir:
            messagebox.showwarning("警告", "请选择输出目录")
            return
            
        # 获取目标dBFS
        try:
            target_dbfs = float(self.target_dbfs.get())
        except ValueError:
            messagebox.showerror("错误", "目标dBFS必须是有效的数字")
            return
            
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建临时目录（用于URL下载）
        if self.input_mode.get() == "urls":
            self.temp_dir = tempfile.mkdtemp()
        
        # 开始处理
        self.processing = True
        self.process_button.configure(state="disabled")
        self.progress_bar.set(0)
        
        # 在新线程中处理
        threading.Thread(
            target=self.process_files,
            args=(input_files, output_dir, target_dbfs),
            daemon=True
        ).start()

    def process_files(self, input_files, output_dir, target_dbfs):
        total_files = len(input_files)
        processed = 0
        success_count = 0
        failed_files = []
        
        try:
            with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                futures = []
                
                for input_file in input_files:
                    if self.input_mode.get() == "urls":
                        # 下载URL
                        try:
                            input_file = download_audio(input_file, self.temp_dir)
                        except Exception as e:
                            failed_files.append((input_file, str(e)))
                            continue
                    
                    # 提交处理任务
                    future = executor.submit(
                        process_audio_file,
                        input_file,
                        output_dir,
                        target_dbfs
                    )
                    futures.append((input_file, future))
                
                # 处理结果
                for input_file, future in futures:
                    try:
                        success, result = future.result()
                        if success:
                            success_count += 1
                        else:
                            failed_files.append((input_file, result))
                    except Exception as e:
                        failed_files.append((input_file, str(e)))
                    
                    processed += 1
                    self.update_progress(processed, total_files)
        
        finally:
            # 清理临时目录
            if self.temp_dir and os.path.exists(self.temp_dir):
                try:
                    import shutil
                    shutil.rmtree(self.temp_dir)
                except:
                    pass
            
            # 更新UI
            self.after(0, self.processing_completed, success_count, failed_files)

    def update_progress(self, processed, total):
        self.after(0, lambda: self.progress_bar.set(processed / total))
        self.after(0, lambda: self.progress_label.configure(
            text=f"{processed}/{total}"
        ))

    def processing_completed(self, success_count, failed_files):
        self.processing = False
        self.process_button.configure(state="normal")
        
        # 显示结果
        message = f"处理完成！\n成功: {success_count} 个文件"
        if failed_files:
            message += f"\n失败: {len(failed_files)} 个文件"
            message += "\n\n失败详情:"
            for file, error in failed_files:
                message += f"\n{os.path.basename(file)}: {error}"
        
        messagebox.showinfo("处理结果", message)
        self.status_label.configure(text="处理完成")

    def start_detection(self):
        if self.processing:
            return
            
        # 获取输入文件
        input_files = self.get_input_files()
        if not input_files:
            messagebox.showwarning("警告", "请先选择输入文件/目录/URL")
            return
            
        # 开始处理
        self.processing = True
        self.detect_button.configure(state="disabled")
        self.process_button.configure(state="disabled")
        self.progress_bar.set(0)
        self.audio_info = []  # 清空之前的信息
        
        # 在新线程中处理
        threading.Thread(
            target=self.detect_files,
            args=(input_files,),
            daemon=True
        ).start()

    def detect_files(self, input_files):
        total_files = len(input_files)
        processed = 0
        failed_files = []
        
        try:
            with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                futures = []
                
                for input_file in input_files:
                    if self.input_mode.get() == "urls":
                        # 下载URL
                        try:
                            local_file = download_audio(input_file, self.temp_dir)
                        except Exception as e:
                            failed_files.append((input_file, str(e)))
                            continue
                    else:
                        local_file = input_file
                    
                    # 提交检测任务
                    future = executor.submit(
                        self.detect_single_file,
                        local_file,
                        input_file if self.input_mode.get() == "urls" else None
                    )
                    futures.append((input_file, future))
                
                # 处理结果
                for input_file, future in futures:
                    try:
                        result = future.result()
                        if result:
                            self.audio_info.append(result)
                    except Exception as e:
                        failed_files.append((input_file, str(e)))
                    
                    processed += 1
                    self.update_progress(processed, total_files)
        
        finally:
            # 清理临时目录
            if self.temp_dir and os.path.exists(self.temp_dir):
                try:
                    import shutil
                    shutil.rmtree(self.temp_dir)
                except:
                    pass
            
            # 导出Excel
            if self.audio_info:
                self.export_to_excel()
            
            # 更新UI
            self.after(0, self.detection_completed, failed_files)

    def detect_single_file(self, file_path, original_url=None):
        try:
            data, sr = sf.read(file_path)
            dbfs = get_audio_dbfs(data)
            return {
                '序号': len(self.audio_info) + 1,
                '文件路径': original_url if original_url else file_path,
                'dBFS值': round(dbfs, 2)
            }
        except Exception as e:
            raise Exception(f"处理文件失败: {str(e)}")

    def export_to_excel(self):
        try:
            df = pd.DataFrame(self.audio_info)
            output_dir = self.output_dir_entry.get().strip()
            if not output_dir:
                output_dir = os.path.expanduser("~/Desktop")
            
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            excel_path = os.path.join(output_dir, f"audio_dbfs_report_{timestamp}.xlsx")
            
            df.to_excel(excel_path, index=False)
            messagebox.showinfo("导出成功", f"Excel报告已保存至：\n{excel_path}")
        except Exception as e:
            messagebox.showerror("导出失败", f"导出Excel报告时出错：{str(e)}")

    def detection_completed(self, failed_files):
        self.processing = False
        self.detect_button.configure(state="normal")
        self.process_button.configure(state="normal")
        
        # 显示结果
        message = f"检测完成！\n成功: {len(self.audio_info)} 个文件"
        if failed_files:
            message += f"\n失败: {len(failed_files)} 个文件"
            message += "\n\n失败详情:"
            for file, error in failed_files:
                message += f"\n{os.path.basename(file)}: {error}"
        
        messagebox.showinfo("检测结果", message)
        self.status_label.configure(text="检测完成")

if __name__ == "__main__":
    app = BatchAudioNormalizer()
    app.mainloop() 