import customtkinter as ctk
import asyncio
import threading
from tts_service import TTSService
import os
from tkinter import filedialog
import json
import sys

print("Python version:", sys.version)
print("Current working directory:", os.getcwd())

class TTSApp(ctk.CTk):
    def __init__(self):
        print("Initializing TTSApp...")
        super().__init__()

        # 配置窗口
        self.title("语音合成工具")
        self.geometry("800x600")
        
        # 加载配置
        self.load_config()
        
        # 创建主框架
        self.create_main_frame()
        print("TTSApp initialized successfully")
        
    def load_config(self):
        """加载配置文件"""
        print("Loading configuration...")
        self.config = {
            "appid": "",
            "token": "",
            "cluster": "",
            "voice_type": "zh_female_1"  # 默认音色
        }
        
        # 尝试从文件加载配置
        if os.path.exists("tts_config.json"):
            try:
                with open("tts_config.json", "r", encoding="utf-8") as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
                print("Configuration loaded from file")
            except Exception as e:
                print("Error loading configuration:", str(e))
        else:
            print("No configuration file found")

    def save_config(self):
        """保存配置到文件"""
        try:
            self.update_config() # 先更新config字典
            with open("tts_config.json", "w", encoding="utf-8") as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print("Configuration saved")
            self.status_label.configure(text="配置已保存！", text_color="green")
        except Exception as e:
            print("Error saving configuration:", str(e))
            self.status_label.configure(text=f"保存配置失败: {str(e)}", text_color="red")

    def create_main_frame(self):
        """创建主界面"""
        # 创建配置框架
        config_frame = ctk.CTkFrame(self)
        config_frame.pack(fill="x", padx=20, pady=10)
        
        # AppID
        ctk.CTkLabel(config_frame, text="AppID:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.appid_entry = ctk.CTkEntry(config_frame, width=300)
        self.appid_entry.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        self.appid_entry.insert(0, self.config["appid"])
        
        # Token
        ctk.CTkLabel(config_frame, text="Token:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.token_entry = ctk.CTkEntry(config_frame, width=300)
        self.token_entry.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        self.token_entry.insert(0, self.config["token"])
        
        # Cluster
        ctk.CTkLabel(config_frame, text="Cluster:").grid(row=2, column=0, padx=5, pady=5, sticky="w")
        self.cluster_entry = ctk.CTkEntry(config_frame, width=300)
        self.cluster_entry.grid(row=2, column=1, padx=5, pady=5, sticky="ew")
        self.cluster_entry.insert(0, self.config["cluster"])
        
        # 保存配置按钮
        save_config_btn = ctk.CTkButton(config_frame, text="保存配置", command=self.save_config)
        save_config_btn.grid(row=3, column=0, columnspan=2, pady=10)
        config_frame.columnconfigure(1, weight=1) # Allow the entry to expand
        
        # 创建文本输入框架
        text_frame = ctk.CTkFrame(self)
        text_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 文本输入
        ctk.CTkLabel(text_frame, text="输入文本:").pack(anchor="w", padx=5, pady=5)
        self.text_input = ctk.CTkTextbox(text_frame, height=100)
        self.text_input.pack(fill="both", expand=True, padx=5, pady=5) # Allow textbox to expand
        
        # 参数设置框架
        params_frame = ctk.CTkFrame(text_frame)
        params_frame.pack(fill="x", padx=5, pady=10)
        
        # 音色选择
        ctk.CTkLabel(params_frame, text="音色:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.voice_type = ctk.CTkComboBox(params_frame, 
                                         values=["zh_female_1", "zh_male_1"],
                                         width=150)
        self.voice_type.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        self.voice_type.set(self.config.get("voice_type", "zh_female_1")) # 使用get方法并提供默认值
        
        # 语速设置
        ctk.CTkLabel(params_frame, text="语速:").grid(row=0, column=2, padx=5, pady=5, sticky="w")
        self.speed_slider = ctk.CTkSlider(params_frame, from_=0.5, to=2.0, number_of_steps=30)
        self.speed_slider.grid(row=0, column=3, padx=5, pady=5, sticky="ew")
        self.speed_slider.set(1.0)
        
        # 音量设置
        ctk.CTkLabel(params_frame, text="音量:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.volume_slider = ctk.CTkSlider(params_frame, from_=0.5, to=2.0, number_of_steps=30)
        self.volume_slider.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        self.volume_slider.set(1.0)
        
        # 音调设置
        ctk.CTkLabel(params_frame, text="音调:").grid(row=1, column=2, padx=5, pady=5, sticky="w")
        self.pitch_slider = ctk.CTkSlider(params_frame, from_=0.5, to=2.0, number_of_steps=30)
        self.pitch_slider.grid(row=1, column=3, padx=5, pady=5, sticky="ew")
        self.pitch_slider.set(1.0)
        
        # 允许参数设置框架中的列扩展
        params_frame.columnconfigure(1, weight=1)
        params_frame.columnconfigure(3, weight=1)
        
        # 输出文件选择
        file_frame = ctk.CTkFrame(text_frame)
        file_frame.pack(fill="x", padx=5, pady=10)
        
        ctk.CTkLabel(file_frame, text="输出文件:").pack(side="left", padx=5, pady=5)
        self.output_file = ctk.CTkEntry(file_frame, width=400)
        self.output_file.pack(side="left", fill="x", expand=True, padx=5, pady=5) # Allow entry to expand
        self.output_file.insert(0, "output.mp3")
        
        browse_btn = ctk.CTkButton(file_frame, text="选择文件", command=self.browse_file)
        browse_btn.pack(side="left", padx=5, pady=5)
        
        # 合成按钮
        self.synthesize_btn = ctk.CTkButton(text_frame, text="开始合成", command=self.start_synthesis)
        self.synthesize_btn.pack(pady=10)
        
        # 状态标签
        self.status_label = ctk.CTkLabel(text_frame, text="", text_color="gray")
        self.status_label.pack(pady=5)
        
    def browse_file(self):
        """选择输出文件"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".mp3",
            filetypes=[("MP3 files", "*.mp3"), ("All files", "*.*")]
        )
        if filename:
            self.output_file.delete(0, "end")
            self.output_file.insert(0, filename)
            
    def update_config(self):
        """更新配置"""
        self.config["appid"] = self.appid_entry.get()
        self.config["token"] = self.token_entry.get()
        self.config["cluster"] = self.cluster_entry.get()
        self.config["voice_type"] = self.voice_type.get()
        
    def start_synthesis(self):
        """开始合成"""
        # 更新配置
        self.update_config()
        
        # 获取参数
        text = self.text_input.get("1.0", "end-1c")
        if not text.strip(): # Check if text is empty or just whitespace
            self.status_label.configure(text="请输入要合成的文本！", text_color="red")
            return
            
        output_file = self.output_file.get()
        if not output_file:
            self.status_label.configure(text="请选择输出文件！", text_color="red")
            return
            
        # 禁用按钮
        self.synthesize_btn.configure(state="disabled")
        self.status_label.configure(text="正在合成...", text_color="yellow")
        
        # 在新线程中运行合成，防止阻塞主UI线程
        thread = threading.Thread(target=self.run_synthesis, args=(text, output_file))
        thread.start()
        
    def run_synthesis(self, text, output_file):
        """运行合成任务"""
        try:
            # 创建事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 创建TTS服务实例
            tts = TTSService(
                appid=self.config["appid"],
                token=self.config["token"],
                cluster=self.config["cluster"]
            )
            
            # 运行合成
            success = loop.run_until_complete(tts.synthesize(
                text=text,
                voice_type=self.config["voice_type"],
                output_file=output_file,
                speed_ratio=self.speed_slider.get(),
                volume_ratio=self.volume_slider.get(),
                pitch_ratio=self.pitch_slider.get()
            ))
            
            # 更新UI
            if success:
                # 使用after方法在主线程更新UI
                self.after(0, self.status_label.configure, {"text": "合成成功！", "text_color": "green"})
            else:
                 # 使用after方法在主线程更新UI
                self.after(0, self.status_label.configure, {"text": "合成失败！", "text_color": "red"})
                
        except Exception as e:
             # 使用after方法在主线程更新UI
            self.after(0, self.status_label.configure, {"text": f"错误：{str(e)}", "text_color": "red"})
        finally:
             # 使用after方法在主线程更新UI
            self.after(0, self.synthesize_btn.configure, {"state": "normal"})
            if loop and loop.is_running():
                 loop.stop()
            if loop and not loop.is_closed():
                 loop.close()

if __name__ == "__main__":
    app = TTSApp()
    app.mainloop() 