import asyncio
import websockets
import uuid
import json
import gzip
import copy
from typing import Optional, Dict, Any

class TTSService:
    def __init__(self, appid: str, token: str, cluster: str):
        """
        初始化TTS服务
        
        Args:
            appid: 应用ID
            token: 访问令牌
            cluster: 集群信息
        """
        self.appid = appid
        self.token = token
        self.cluster = cluster
        self.host = "openspeech.bytedance.com"
        self.api_url = f"wss://{self.host}/api/v1/tts/ws_binary"
        
        # 默认消息头
        self.default_header = bytearray(b'\x11\x10\x11\x00')
        
        # 消息类型定义
        self.MESSAGE_TYPES = {
            11: "audio-only server response",
            12: "frontend server response",
            15: "error message from server"
        }
        
    async def synthesize(self, 
                        text: str,
                        voice_type: str,
                        output_file: str,
                        speed_ratio: float = 1.0,
                        volume_ratio: float = 1.0,
                        pitch_ratio: float = 1.0) -> bool:
        """
        合成语音
        
        Args:
            text: 要合成的文本
            voice_type: 音色类型
            output_file: 输出文件路径
            speed_ratio: 语速比例
            volume_ratio: 音量比例
            pitch_ratio: 音调比例
            
        Returns:
            bool: 合成是否成功
        """
        request_json = {
            "app": {
                "appid": self.appid,
                "token": self.token,
                "cluster": self.cluster
            },
            "user": {
                "uid": str(uuid.uuid4())
            },
            "audio": {
                "voice_type": voice_type,
                "encoding": "mp3",
                "speed_ratio": speed_ratio,
                "volume_ratio": volume_ratio,
                "pitch_ratio": pitch_ratio,
            },
            "request": {
                "reqid": str(uuid.uuid4()),
                "text": text,
                "text_type": "plain",
                "operation": "submit"
            }
        }
        
        # 准备请求数据
        payload_bytes = str.encode(json.dumps(request_json))
        payload_bytes = gzip.compress(payload_bytes)
        full_client_request = bytearray(self.default_header)
        full_client_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
        full_client_request.extend(payload_bytes)
        
        try:
            header = {"Authorization": f"Bearer; {self.token}"}
            async with websockets.connect(self.api_url, extra_headers=header, ping_interval=None) as ws:
                await ws.send(full_client_request)
                
                with open(output_file, "wb") as f:
                    while True:
                        res = await ws.recv()
                        done = self._parse_response(res, f)
                        if done:
                            break
                return True
        except Exception as e:
            print(f"合成失败: {str(e)}")
            return False
            
    def _parse_response(self, res: bytes, file) -> bool:
        """
        解析服务器响应
        
        Args:
            res: 响应数据
            file: 输出文件对象
            
        Returns:
            bool: 是否处理完成
        """
        protocol_version = res[0] >> 4
        header_size = res[0] & 0x0f
        message_type = res[1] >> 4
        message_type_specific_flags = res[1] & 0x0f
        serialization_method = res[2] >> 4
        message_compression = res[2] & 0x0f
        reserved = res[3]
        header_extensions = res[4:header_size*4]
        payload = res[header_size*4:]
        
        if message_type == 0xb:  # audio-only server response
            if message_type_specific_flags == 0:  # no sequence number as ACK
                return False
            else:
                sequence_number = int.from_bytes(payload[:4], "big", signed=True)
                payload_size = int.from_bytes(payload[4:8], "big", signed=False)
                payload = payload[8:]
                file.write(payload)
                return sequence_number < 0
                
        elif message_type == 0xf:  # error message
            code = int.from_bytes(payload[:4], "big", signed=False)
            msg_size = int.from_bytes(payload[4:8], "big", signed=False)
            error_msg = payload[8:]
            if message_compression == 1:
                error_msg = gzip.decompress(error_msg)
            error_msg = str(error_msg, "utf-8")
            print(f"错误代码: {code}")
            print(f"错误信息: {error_msg}")
            return True
            
        return True

# 使用示例
async def main():
    # 配置信息
    appid = "your_appid"
    token = "your_token"
    cluster = "your_cluster"
    
    # 创建TTS服务实例
    tts = TTSService(appid, token, cluster)
    
    # 合成语音
    success = await tts.synthesize(
        text="这是一段测试文本",
        voice_type="your_voice_type",
        output_file="output.mp3",
        speed_ratio=1.0,
        volume_ratio=1.0,
        pitch_ratio=1.0
    )
    
    if success:
        print("语音合成成功！")
    else:
        print("语音合成失败！")

if __name__ == "__main__":
    asyncio.run(main()) 