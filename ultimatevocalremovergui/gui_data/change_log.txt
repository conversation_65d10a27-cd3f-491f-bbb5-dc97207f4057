Most Recent Changes:

~ Fixed Download Center model list issue.
~ Fixed audio clip in ensemble mode.
~ Fixed output model name issue in ensemble mode.
~ Added "Batch Mode" for MDX-Net to increase performance.
    ~ Batch Mode is more memory efficient.
    ~ Batch Mode produces the best output, regardless of batch size.
~ Added Batch Mode for VR Architecture.
~ Added Mixer Mode for Demucs.
    ~ This option may improve separation for some 4-stem models.

Fixes & Changes going from UVR v5.4 to v5.5:

~ The progress bar is now fully synced up with every process in the application.
~ Fixed low-resolution icon.
~ Added the ability to download models manually if the application can't connect 
   to the internet.
~ Drag-n-drop is functional across all os platforms.
~ Resolved mp3 tag issue in MacOS version.

Performance:

~ Model load times are faster.
~ Importing/exporting audio files is faster.

MacOS M1 Notes:

~ The GPU Conversion checkbox will enable MPS for GPU acceleration. However,
   only the VR Architecture models are currently compatible with it.

New Options:

~ Select Saved Settings option - Allows the user to save the current settings 
   of the whole application. You can also load a saved setting or reset them to 
   the default.
~ Right-click menu - Allows for quick access to important options.
~ Help Hints option - When enabled, users can hover over options to see a pop-up 
   text that describes that option. The right-clicking option also allows copying 
   the "Help Hint" text.
~ Secondary Model Mode - This option is an expanded version of the "Demucs Model" 
   option that was only available to MDX-Net. Except now, this option is available 
   in all three AI Networks and for any stem. Any model can now be Secondary, and 
   the user can choose the amount of influence it has on the final result.
~ Robust caching for ensemble mode, allowing for much faster processing times.
~ Clicking the "Input" field will pop up a window allowing the user to review the selected audio inputs. Within this menu, users can:
    ~ Remove inputs.
    ~ Verify inputs.
    ~ Create samples of chosen inputs.
~ "Sample Mode" option - Allows the user to process only part of a track to sample 
   settings or a model without running a full conversion.
    ~ The number in the parentheses is the current number of seconds the generated 
       sample will be.
    ~ You can choose the number of seconds to extract from the track in the "Additional 
       Settings" menu.

VR Architecture:

~ Ability to toggle "High-End Processing."
~ Ability to change the post-processing threshold.
~ Support for the latest VR architecture
    ~ Crop Size and Batch Size are specifically for models using the latest 
       architecture only.

MDX-NET:

~ Denoise Output option results in cleaner results, 
   but the processing time will be longer. This option has replaced Noise Reduction.
~ Spectral Inversion option uses spectral inversion techniques for a 
   cleaner secondary stem result. This option may slow down the audio export process.
~ Secondary stem now has the same frequency cut-off as the main stem.

Demucs:

~ Demucs v4 models are now supported, including the 6-stem model.
~ Ability to combine remaining stems instead of inverting selected stem with the 
   mixture only when a user does not select "All Stems".
~ A Pre-process model that allows the user to run an inference through a robust 
   vocal or instrumental model and separate the remaining stems from its generated 
   instrumental mix. This option can significantly reduce vocal bleed in other 
   Demucs-generated non-vocal stems.
    ~ The Pre-process model is intended for Demucs separations for all stems except
       vocals and instrumentals.

Ensemble Mode: 

~ Ensemble Mode has been extended to include the following:
    ~ Averaging is a new algorithm that averages the final results.
    ~ Unlimited models in the ensemble.
    ~ Ability to save different ensembles.
    ~ Ability to ensemble outputs for all individual stem types.
    ~ Ability to choose unique ensemble algorithms.
    ~ Ability to ensemble all 4 Demucs stems at once.