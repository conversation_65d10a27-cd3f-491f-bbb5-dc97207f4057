import os
import sqlite3
import cv2
import imagehash
from PIL import Image
from flask import Flask, request, render_template_string, redirect, url_for

UPLOAD_FOLDER = 'uploads'
DB_PATH = 'fingerprints.db'
ALLOWED_EXTENSIONS = {'mp4', 'mov', 'avi'}

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

# 初始化数据库
def init_db():
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('''
    CREATE TABLE IF NOT EXISTS fingerprints (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        video_name TEXT,
        frame_index INTEGER,
        hash TEXT
    )
    ''')
    conn.commit()
    conn.close()

init_db()

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def extract_and_store_phash(video_path, video_name, frame_interval=30):
    cap = cv2.VideoCapture(video_path)
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    frame_count = 0
    index = 0

    while True:
        ret, frame = cap.read()
        if not ret:
            break
        if frame_count % frame_interval == 0:
            pil_img = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
            hash_val = imagehash.phash(pil_img)
            c.execute('INSERT INTO fingerprints (video_name, frame_index, hash) VALUES (?, ?, ?)',
                      (video_name, index, str(hash_val)))
            index += 1
        frame_count += 1

    conn.commit()
    conn.close()
    cap.release()

def get_video_fingerprint(video_name):
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('SELECT frame_index, hash FROM fingerprints WHERE video_name=?', (video_name,))
    result = c.fetchall()
    conn.close()
    return result

def compare_fingerprints(video1, video2):
    fp1 = get_video_fingerprint(video1)
    fp2 = get_video_fingerprint(video2)

    if not fp1 or not fp2:
        return None

    min_len = min(len(fp1), len(fp2))
    distances = []
    for i in range(min_len):
        h1 = imagehash.hex_to_hash(fp1[i][1])
        h2 = imagehash.hex_to_hash(fp2[i][1])
        distances.append(h1 - h2)

    avg_distance = sum(distances) / len(distances)
    return avg_distance, distances

@app.route('/', methods=['GET', 'POST'])
def upload_file():
    if request.method == 'POST':
        files = request.files.getlist('file')
        if not files:
            return '没有选择文件'

        uploaded = []
        for file in files:
            if file.filename == '':
                continue
            if file and allowed_file(file.filename):
                filename = file.filename
                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                file.save(filepath)
                extract_and_store_phash(filepath, filename)
                uploaded.append(filename)

        return render_template_string('''
        <!doctype html>
        <title>上传完成</title>
        <h1>以下视频已上传并提取指纹：</h1>
        <ul>
        {% for f in files %}
            <li><a href="{{ url_for('show_fingerprint', filename=f) }}">{{ f }}</a></li>
        {% endfor %}
        </ul>
        <a href="/">返回上传页面</a><br>
        <a href="/compare">去视频指纹比对页面</a>
        ''', files=uploaded)

    return render_template_string('''
    <!doctype html>
    <title>上传多个视频文件</title>
    <h1>上传视频文件（可多选）</h1>
    <form method=post enctype=multipart/form-data>
      <input type="file" name="file" multiple>
      <input type="submit" value="上传并提取指纹">
    </form>
    ''')

@app.route('/fingerprint/<filename>')
def show_fingerprint(filename):
    hashes = get_video_fingerprint(filename)
    return render_template_string('''
    <!doctype html>
    <title>视频指纹提取结果</title>
    <h1>视频 {{ filename }} 的指纹：</h1>
    <pre>{% for frame_index, hash in hashes %}[{{ frame_index }}] {{ hash }}
{% endfor %}</pre>
    <a href="/">返回上传页面</a><br>
    <a href="/compare">去视频指纹比对页面</a>
    ''', filename=filename, hashes=hashes)

@app.route('/compare', methods=['GET', 'POST'])
def compare():
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('SELECT DISTINCT video_name FROM fingerprints')
    videos = [row[0] for row in c.fetchall()]
    conn.close()

    if request.method == 'POST':
        v1 = request.form['video1']
        v2 = request.form['video2']
        result = compare_fingerprints(v1, v2)
        if result is None:
            return "有视频没有提取指纹"
        avg_dist, dists = result
        return render_template_string('''
        <!doctype html>
        <title>视频指纹比对</title>
        <h1>视频 {{ v1 }} 与 {{ v2 }} 的指纹比对</h1>
        <p>平均海明距离（越小越相似）：{{ avg }}</p>
        <pre>{{ dists }}</pre>
        <a href="/compare">返回比对页面</a><br>
        <a href="/">返回上传页面</a>
        ''', v1=v1, v2=v2, avg=avg_dist, dists=dists)

    return render_template_string('''
    <!doctype html>
    <title>视频指纹比对</title>
    <h1>选择两个视频进行指纹相似度比对</h1>
    <form method=post>
      <label>视频 1:</label>
      <select name="video1">
        {% for v in videos %}<option value="{{ v }}">{{ v }}</option>{% endfor %}
      </select><br><br>
      <label>视频 2:</label>
      <select name="video2">
        {% for v in videos %}<option value="{{ v }}">{{ v }}</option>{% endfor %}
      </select><br><br>
      <input type=submit value="比对">
    </form>
    <a href="/">返回上传页面</a>
    ''', videos=videos)

if __name__ == '__main__':
    app.run(debug=True, port=5000)