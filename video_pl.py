import cv2
import os
import tkinter as tk
from tkinter import filedialog, messagebox
from tkinter import ttk
from PIL import Image, ImageTk
import threading
import time

def blur_subtitles(video_path, x, y, w, h, blur_strength):
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise IOError("无法打开视频文件")

    # 视频参数
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width  = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    out_path = os.path.splitext(video_path)[0] + "_blurred.mp4"
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(out_path, fourcc, fps, (width, height))

    frame_idx = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break

        # 取出字幕区域，进行高斯模糊
        roi = frame[y:y+h, x:x+w]
        if roi.size > 0:
            blur = cv2.GaussianBlur(roi, (blur_strength|1, blur_strength|1), 0)
            frame[y:y+h, x:x+w] = blur

        out.write(frame)
        frame_idx += 1

    cap.release()
    out.release()
    return out_path

class VideoPreviewWidget:
    def __init__(self, parent, max_width=320, max_height=320):
        self.parent = parent
        self.max_width = max_width
        self.max_height = max_height
        self.width = max_width
        self.height = max_height
        self.video_path = None
        self.cap = None
        self.current_frame = None
        self.scale_x = 1.0
        self.scale_y = 1.0

        # 模糊区域参数
        self.blur_x = 0
        self.blur_y = 600
        self.blur_w = 1280
        self.blur_h = 80
        self.blur_strength = 25

        # 拖拽状态
        self.dragging = False
        self.drag_start_x = 0
        self.drag_start_y = 0
        self.resize_mode = None  # None, 'move', 'resize_br', 'resize_bl', 'resize_tr', 'resize_tl'

        self.setup_ui()

    def setup_ui(self):
        # 创建预览画布容器
        self.canvas_frame = ttk.Frame(self.parent)
        self.canvas_frame.pack(pady=10)

        # 创建预览画布（初始大小，会在加载视频时调整）
        self.canvas = tk.Canvas(self.canvas_frame, width=self.width, height=self.height, bg='black')
        self.canvas.pack()

        # 绑定鼠标事件
        self.canvas.bind("<Button-1>", self.on_mouse_down)
        self.canvas.bind("<B1-Motion>", self.on_mouse_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_mouse_up)
        self.canvas.bind("<Motion>", self.on_mouse_move)

        # 控制按钮框架
        control_frame = ttk.Frame(self.parent)
        control_frame.pack(pady=5)

        self.play_button = ttk.Button(control_frame, text="播放", command=self.toggle_play)
        self.play_button.pack(side=tk.LEFT, padx=5)

        self.prev_button = ttk.Button(control_frame, text="上一帧", command=self.prev_frame)
        self.prev_button.pack(side=tk.LEFT, padx=5)

        self.next_button = ttk.Button(control_frame, text="下一帧", command=self.next_frame)
        self.next_button.pack(side=tk.LEFT, padx=5)

        # 模糊强度滑块
        blur_frame = ttk.Frame(self.parent)
        blur_frame.pack(pady=5)

        ttk.Label(blur_frame, text="模糊强度:").pack(side=tk.LEFT)
        self.blur_scale = ttk.Scale(blur_frame, from_=1, to=99, orient=tk.HORIZONTAL,
                                   command=self.on_blur_change)
        self.blur_scale.set(self.blur_strength)
        self.blur_scale.pack(side=tk.LEFT, padx=5)

        self.blur_label = ttk.Label(blur_frame, text=str(self.blur_strength))
        self.blur_label.pack(side=tk.LEFT, padx=5)

        # 信息显示
        info_frame = ttk.Frame(self.parent)
        info_frame.pack(pady=5)

        self.coord_label = ttk.Label(info_frame, text=f"区域: X={self.blur_x}, Y={self.blur_y}, W={self.blur_w}, H={self.blur_h}")
        self.coord_label.pack()

        self.video_info_label = ttk.Label(info_frame, text="请选择视频文件")
        self.video_info_label.pack()

        self.playing = False
        self.play_thread = None

    def load_video(self, video_path):
        """加载视频文件"""
        if self.cap:
            self.cap.release()

        self.video_path = video_path
        self.cap = cv2.VideoCapture(video_path)

        if not self.cap.isOpened():
            messagebox.showerror("错误", "无法打开视频文件")
            return False

        # 获取视频信息
        self.video_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.video_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        self.fps = self.cap.get(cv2.CAP_PROP_FPS)

        # 计算等比例缩放尺寸
        self.calculate_preview_size()

        # 更新画布尺寸
        self.canvas.config(width=self.width, height=self.height)

        # 更新视频信息显示
        scale_percent = int(self.scale_x * 100)
        self.video_info_label.config(
            text=f"视频尺寸: {self.video_width}x{self.video_height} | 预览尺寸: {self.width}x{self.height} | 缩放: {scale_percent}%"
        )

        # 读取第一帧
        ret, frame = self.cap.read()
        if ret:
            self.current_frame = frame
            self.display_frame()

        return True

    def calculate_preview_size(self):
        """计算预览窗口的等比例缩放尺寸"""
        # 计算缩放比例，确保高度不超过max_height
        scale_by_width = self.max_width / self.video_width
        scale_by_height = self.max_height / self.video_height

        # 选择较小的缩放比例以确保视频完全显示在预览窗口内
        scale = min(scale_by_width, scale_by_height)

        # 计算实际预览尺寸
        self.width = int(self.video_width * scale)
        self.height = int(self.video_height * scale)

        # 确保高度不超过最大限制
        if self.height > self.max_height:
            self.height = self.max_height
            self.width = int(self.video_width * (self.max_height / self.video_height))

        # 计算缩放比例
        self.scale_x = self.width / self.video_width
        self.scale_y = self.height / self.video_height

    def display_frame(self):
        """显示当前帧"""
        if self.current_frame is None:
            return

        # 缩放帧到预览尺寸
        frame = cv2.resize(self.current_frame, (self.width, self.height))

        # 应用模糊效果预览
        preview_frame = frame.copy()
        scaled_x = int(self.blur_x * self.scale_x)
        scaled_y = int(self.blur_y * self.scale_y)
        scaled_w = int(self.blur_w * self.scale_x)
        scaled_h = int(self.blur_h * self.scale_y)

        # 确保坐标在有效范围内
        scaled_x = max(0, min(scaled_x, self.width))
        scaled_y = max(0, min(scaled_y, self.height))
        scaled_w = max(1, min(scaled_w, self.width - scaled_x))
        scaled_h = max(1, min(scaled_h, self.height - scaled_y))

        # 应用模糊
        if scaled_w > 0 and scaled_h > 0:
            roi = preview_frame[scaled_y:scaled_y+scaled_h, scaled_x:scaled_x+scaled_w]
            if roi.size > 0:
                blur_size = max(1, int(self.blur_strength * min(self.scale_x, self.scale_y)))
                if blur_size % 2 == 0:
                    blur_size += 1
                blur = cv2.GaussianBlur(roi, (blur_size, blur_size), 0)
                preview_frame[scaled_y:scaled_y+scaled_h, scaled_x:scaled_x+scaled_w] = blur

        # 转换为RGB并显示
        frame_rgb = cv2.cvtColor(preview_frame, cv2.COLOR_BGR2RGB)
        image = Image.fromarray(frame_rgb)
        photo = ImageTk.PhotoImage(image)

        self.canvas.delete("all")
        self.canvas.create_image(self.width//2, self.height//2, image=photo)
        self.canvas.image = photo  # 保持引用

        # 绘制选择框
        self.draw_selection_box()

    def draw_selection_box(self):
        """绘制模糊区域选择框"""
        scaled_x = int(self.blur_x * self.scale_x)
        scaled_y = int(self.blur_y * self.scale_y)
        scaled_w = int(self.blur_w * self.scale_x)
        scaled_h = int(self.blur_h * self.scale_y)

        # 绘制主矩形
        self.canvas.create_rectangle(scaled_x, scaled_y, scaled_x + scaled_w, scaled_y + scaled_h,
                                   outline='red', width=2, tags='selection')

        # 绘制调整手柄
        handle_size = 8
        handles = [
            (scaled_x - handle_size//2, scaled_y - handle_size//2, 'tl'),  # 左上
            (scaled_x + scaled_w - handle_size//2, scaled_y - handle_size//2, 'tr'),  # 右上
            (scaled_x - handle_size//2, scaled_y + scaled_h - handle_size//2, 'bl'),  # 左下
            (scaled_x + scaled_w - handle_size//2, scaled_y + scaled_h - handle_size//2, 'br'),  # 右下
        ]

        for hx, hy, tag in handles:
            self.canvas.create_rectangle(hx, hy, hx + handle_size, hy + handle_size,
                                       fill='red', outline='white', tags=f'handle_{tag}')

        # 更新坐标显示
        self.coord_label.config(text=f"区域: X={self.blur_x}, Y={self.blur_y}, W={self.blur_w}, H={self.blur_h}")

    def get_handle_at_pos(self, x, y):
        """检查鼠标位置是否在调整手柄上"""
        scaled_x = int(self.blur_x * self.scale_x)
        scaled_y = int(self.blur_y * self.scale_y)
        scaled_w = int(self.blur_w * self.scale_x)
        scaled_h = int(self.blur_h * self.scale_y)

        handle_size = 8
        handles = {
            'tl': (scaled_x - handle_size//2, scaled_y - handle_size//2),
            'tr': (scaled_x + scaled_w - handle_size//2, scaled_y - handle_size//2),
            'bl': (scaled_x - handle_size//2, scaled_y + scaled_h - handle_size//2),
            'br': (scaled_x + scaled_w - handle_size//2, scaled_y + scaled_h - handle_size//2),
        }

        for handle, (hx, hy) in handles.items():
            if hx <= x <= hx + handle_size and hy <= y <= hy + handle_size:
                return handle
        return None

    def is_inside_selection(self, x, y):
        """检查鼠标位置是否在选择区域内"""
        scaled_x = int(self.blur_x * self.scale_x)
        scaled_y = int(self.blur_y * self.scale_y)
        scaled_w = int(self.blur_w * self.scale_x)
        scaled_h = int(self.blur_h * self.scale_y)

        return scaled_x <= x <= scaled_x + scaled_w and scaled_y <= y <= scaled_y + scaled_h

    def on_mouse_down(self, event):
        """鼠标按下事件"""
        self.drag_start_x = event.x
        self.drag_start_y = event.y

        # 检查是否点击在手柄上
        handle = self.get_handle_at_pos(event.x, event.y)
        if handle:
            self.resize_mode = f'resize_{handle}'
            self.dragging = True
        elif self.is_inside_selection(event.x, event.y):
            self.resize_mode = 'move'
            self.dragging = True
        else:
            self.dragging = False

    def on_mouse_drag(self, event):
        """鼠标拖拽事件"""
        if not self.dragging:
            return

        dx = event.x - self.drag_start_x
        dy = event.y - self.drag_start_y

        # 转换为原始视频坐标
        dx_orig = int(dx / self.scale_x)
        dy_orig = int(dy / self.scale_y)

        if self.resize_mode == 'move':
            # 移动整个区域
            self.blur_x = max(0, min(self.blur_x + dx_orig, self.video_width - self.blur_w))
            self.blur_y = max(0, min(self.blur_y + dy_orig, self.video_height - self.blur_h))
        elif self.resize_mode == 'resize_br':
            # 右下角调整
            new_w = max(10, self.blur_w + dx_orig)
            new_h = max(10, self.blur_h + dy_orig)
            self.blur_w = min(new_w, self.video_width - self.blur_x)
            self.blur_h = min(new_h, self.video_height - self.blur_y)
        elif self.resize_mode == 'resize_tl':
            # 左上角调整
            new_x = max(0, self.blur_x + dx_orig)
            new_y = max(0, self.blur_y + dy_orig)
            self.blur_w = max(10, self.blur_w - (new_x - self.blur_x))
            self.blur_h = max(10, self.blur_h - (new_y - self.blur_y))
            self.blur_x = new_x
            self.blur_y = new_y
        elif self.resize_mode == 'resize_tr':
            # 右上角调整
            new_y = max(0, self.blur_y + dy_orig)
            new_w = max(10, self.blur_w + dx_orig)
            self.blur_h = max(10, self.blur_h - (new_y - self.blur_y))
            self.blur_y = new_y
            self.blur_w = min(new_w, self.video_width - self.blur_x)
        elif self.resize_mode == 'resize_bl':
            # 左下角调整
            new_x = max(0, self.blur_x + dx_orig)
            new_h = max(10, self.blur_h + dy_orig)
            self.blur_w = max(10, self.blur_w - (new_x - self.blur_x))
            self.blur_x = new_x
            self.blur_h = min(new_h, self.video_height - self.blur_y)

        self.drag_start_x = event.x
        self.drag_start_y = event.y
        self.display_frame()

    def on_mouse_up(self, event):
        """鼠标释放事件"""
        self.dragging = False
        self.resize_mode = None

    def on_mouse_move(self, event):
        """鼠标移动事件（用于改变光标）"""
        if self.dragging:
            return

        handle = self.get_handle_at_pos(event.x, event.y)
        if handle:
            if handle in ['tl', 'br']:
                self.canvas.config(cursor='size_nw_se')
            elif handle in ['tr', 'bl']:
                self.canvas.config(cursor='size_ne_sw')
        elif self.is_inside_selection(event.x, event.y):
            self.canvas.config(cursor='fleur')
        else:
            self.canvas.config(cursor='arrow')

    def on_blur_change(self, value):
        """模糊强度改变事件"""
        self.blur_strength = int(float(value))
        if self.blur_strength % 2 == 0:
            self.blur_strength += 1
        if hasattr(self, 'blur_label'):
            self.blur_label.config(text=str(self.blur_strength))
        if hasattr(self, 'current_frame') and self.current_frame is not None:
            self.display_frame()

    def toggle_play(self):
        """切换播放/暂停"""
        if not self.cap:
            return

        if self.playing:
            self.playing = False
            self.play_button.config(text="播放")
        else:
            self.playing = True
            self.play_button.config(text="暂停")
            if self.play_thread is None or not self.play_thread.is_alive():
                self.play_thread = threading.Thread(target=self.play_video)
                self.play_thread.daemon = True
                self.play_thread.start()

    def play_video(self):
        """播放视频线程"""
        while self.playing and self.cap:
            ret, frame = self.cap.read()
            if not ret:
                # 视频结束，重新开始
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                continue

            self.current_frame = frame
            self.parent.after(0, self.display_frame)

            # 控制播放速度
            time.sleep(1.0 / self.fps)

    def prev_frame(self):
        """上一帧"""
        if not self.cap:
            return

        current_pos = self.cap.get(cv2.CAP_PROP_POS_FRAMES)
        new_pos = max(0, current_pos - 1)
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, new_pos)

        ret, frame = self.cap.read()
        if ret:
            self.current_frame = frame
            self.display_frame()

    def next_frame(self):
        """下一帧"""
        if not self.cap:
            return

        ret, frame = self.cap.read()
        if ret:
            self.current_frame = frame
            self.display_frame()

    def get_blur_params(self):
        """获取当前模糊参数"""
        return self.blur_x, self.blur_y, self.blur_w, self.blur_h, self.blur_strength

    def set_blur_params(self, x, y, w, h, strength):
        """设置模糊参数"""
        self.blur_x = x
        self.blur_y = y
        self.blur_w = w
        self.blur_h = h
        self.blur_strength = strength
        self.blur_scale.set(strength)
        self.display_frame()

def launch_gui():
    def choose_file():
        file_path = filedialog.askopenfilename(filetypes=[("MP4 files", "*.mp4"), ("AVI files", "*.avi"), ("MOV files", "*.mov")])
        if file_path:
            file_var.set(file_path)
            if preview_widget.load_video(file_path):
                # 同步预览参数到输入框
                update_entries_from_preview()

    def update_entries_from_preview():
        """从预览组件更新输入框"""
        x, y, w, h, strength = preview_widget.get_blur_params()
        entry_x.delete(0, tk.END)
        entry_x.insert(0, str(x))
        entry_y.delete(0, tk.END)
        entry_y.insert(0, str(y))
        entry_w.delete(0, tk.END)
        entry_w.insert(0, str(w))
        entry_h.delete(0, tk.END)
        entry_h.insert(0, str(h))
        entry_blur.delete(0, tk.END)
        entry_blur.insert(0, str(strength))

    def update_preview_from_entries():
        """从输入框更新预览组件"""
        try:
            x = int(entry_x.get())
            y = int(entry_y.get())
            w = int(entry_w.get())
            h = int(entry_h.get())
            strength = int(entry_blur.get())
            preview_widget.set_blur_params(x, y, w, h, strength)
        except ValueError:
            pass

    def run_blur():
        try:
            if not file_var.get():
                messagebox.showerror("错误", "请先选择视频文件")
                return

            # 从预览组件获取最新参数
            x, y, w, h, strength = preview_widget.get_blur_params()

            output = blur_subtitles(
                file_var.get(),
                x, y, w, h, strength
            )
            messagebox.showinfo("处理完成", f"视频已保存至：\n{output}")
        except Exception as e:
            messagebox.showerror("错误", str(e))

    root = tk.Tk()
    root.title("视频字幕模糊工具 - 可视化调整")
    root.geometry("800x700")

    # 文件选择区域
    file_frame = ttk.Frame(root)
    file_frame.pack(pady=10, padx=10, fill=tk.X)

    file_var = tk.StringVar()
    ttk.Label(file_frame, text="视频文件:").pack(side=tk.LEFT)
    ttk.Entry(file_frame, textvariable=file_var, width=50).pack(side=tk.LEFT, padx=5)
    ttk.Button(file_frame, text="选择文件", command=choose_file).pack(side=tk.LEFT, padx=5)

    # 预览区域
    preview_frame = ttk.LabelFrame(root, text="视频预览")
    preview_frame.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)

    preview_widget = VideoPreviewWidget(preview_frame, max_width=320, max_height=320
    )

    # 参数调整区域
    params_frame = ttk.LabelFrame(root, text="参数调整")
    params_frame.pack(pady=10, padx=10, fill=tk.X)

    # 创建参数输入框
    params_grid = ttk.Frame(params_frame)
    params_grid.pack(pady=10)

    ttk.Label(params_grid, text="X坐标:").grid(row=0, column=0, sticky='e', padx=5)
    entry_x = ttk.Entry(params_grid, width=10)
    entry_x.insert(0, "0")
    entry_x.grid(row=0, column=1, padx=5)
    entry_x.bind('<KeyRelease>', lambda e: update_preview_from_entries())

    ttk.Label(params_grid, text="Y坐标:").grid(row=0, column=2, sticky='e', padx=5)
    entry_y = ttk.Entry(params_grid, width=10)
    entry_y.insert(0, "600")
    entry_y.grid(row=0, column=3, padx=5)
    entry_y.bind('<KeyRelease>', lambda e: update_preview_from_entries())

    ttk.Label(params_grid, text="宽度:").grid(row=1, column=0, sticky='e', padx=5)
    entry_w = ttk.Entry(params_grid, width=10)
    entry_w.insert(0, "1280")
    entry_w.grid(row=1, column=1, padx=5)
    entry_w.bind('<KeyRelease>', lambda e: update_preview_from_entries())

    ttk.Label(params_grid, text="高度:").grid(row=1, column=2, sticky='e', padx=5)
    entry_h = ttk.Entry(params_grid, width=10)
    entry_h.insert(0, "80")
    entry_h.grid(row=1, column=3, padx=5)
    entry_h.bind('<KeyRelease>', lambda e: update_preview_from_entries())

    ttk.Label(params_grid, text="模糊强度:").grid(row=2, column=0, sticky='e', padx=5)
    entry_blur = ttk.Entry(params_grid, width=10)
    entry_blur.insert(0, "25")
    entry_blur.grid(row=2, column=1, padx=5)
    entry_blur.bind('<KeyRelease>', lambda e: update_preview_from_entries())

    # 处理按钮
    button_frame = ttk.Frame(root)
    button_frame.pack(pady=10)

    ttk.Button(button_frame, text="同步预览参数", command=update_entries_from_preview).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="开始处理视频", command=run_blur).pack(side=tk.LEFT, padx=5)

    root.mainloop()

if __name__ == "__main__":
    launch_gui()