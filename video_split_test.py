import cv2
import numpy as np
import os

def detect_scene_changes(video_path, threshold=0.8, frame_skip=0):
    """
    检测视频中的画面切换时间点
    参数：
        video_path: 视频文件路径
        threshold: 直方图差异阈值（0-1，越小越敏感）
        frame_skip: 跳帧处理（0=不跳帧，1=每处理1帧跳1帧，用于加速）
    返回：
        包含切换帧号的列表
    """
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise Exception("无法打开视频文件")

    prev_hist = None
    scene_changes = []
    frame_count = 0

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break

        # 跳帧处理
        if frame_skip > 0:
            for _ in range(frame_skip):
                cap.grab()
                frame_count += 1

        # 转换为HSV颜色空间并计算直方图
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        hist = cv2.calcHist([hsv], [0, 1], None, [16, 16], [0, 180, 0, 256])
        hist = cv2.normalize(hist, hist).flatten()

        if prev_hist is not None:
            # 使用相关性比较直方图
            similarity = cv2.compareHist(hist, prev_hist, cv2.HISTCMP_CORREL)
            
            if similarity < threshold:
                scene_changes.append(frame_count)

        prev_hist = hist
        frame_count += 1

    cap.release()
    return scene_changes

def split_video(video_path, split_frames, output_dir="output"):
    """
    根据帧号分割视频
    参数：
        video_path: 原始视频路径
        split_frames: 分割帧号列表
        output_dir: 输出目录
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise Exception("无法打开视频文件")
    
    # 获取视频参数
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # 添加起始和结束帧号
    split_points = [0] + sorted(split_frames) + [total_frames]
    
    # 初始化视频写入器和当前分割索引
    writers = []
    current_split = 0
    
    for i in range(len(split_points)-1):
        # 创建视频写入器
        output_path = os.path.join(output_dir, f"{i+1}.mp4")
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        writers.append(cv2.VideoWriter(output_path, fourcc, fps, (width, height)))
    
    frame_count = 0
    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        
        # 检查是否需要切换到下一个分割点
        if current_split < len(split_points)-1:
            if frame_count >= split_points[current_split + 1]:
                # 立即切换到下一个分割点
                current_split += 1
        
        # 写入当前分割的视频
        if current_split < len(writers):
            writers[current_split].write(frame)
        
        frame_count += 1
    
    # 释放资源
    cap.release()
    for writer in writers:
        writer.release()
    return [f"{i+1}.mp4" for i in range(len(writers))]

# 使用示例
if __name__ == "__main__":
    # 请将此处替换为您的视频文件路径
    video_path = "test_video.mp4"
    
    if not os.path.exists(video_path):
        print(f"错误：找不到视频文件 '{video_path}'")
        print("请确保视频文件存在，并更新脚本中的 video_path 变量")
        exit(1)
    
    # 检测场景切换（降低阈值到0.5）
    changes = detect_scene_changes(video_path, threshold=0.5, frame_skip=0)
    print("检测到画面切换的帧号：")
    print(changes)
    
    # 分割视频
    output_files = split_video(video_path, changes)
    print("\n生成的分割视频：")
    print(output_files)
    
    
