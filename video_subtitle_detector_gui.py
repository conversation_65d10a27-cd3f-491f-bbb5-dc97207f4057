import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import pytesseract
import os
import threading
import time
from datetime import timedelta
import queue
from PIL import Image, ImageTk
import numpy as np

class VideoSubtitleDetectorGUI:
    def __init__(self, master):
        self.master = master
        master.title("视频字幕变化检测工具")
        master.geometry("1200x800")
        master.resizable(True, True)

        # 初始化变量
        self.video_path = tk.StringVar()
        self.output_dir = tk.StringVar(value=os.getcwd())
        self.frame_threshold = tk.IntVar(value=5000)
        self.subtitle_height = tk.IntVar(value=120)
        self.fps = tk.DoubleVar(value=30.0)
        self.enable_ocr = tk.BooleanVar(value=True)
        self.skip_frames = tk.IntVar(value=1)

        # 视频预览相关变量
        self.video_cap = None
        self.current_frame = None
        self.preview_width = 0
        self.preview_height = 320  # 固定预览高度
        self.scale_factor = 1.0

        # 字幕区域选择相关变量
        self.subtitle_region = [0, 0, 0, 0]  # [x1, y1, x2, y2] 在预览坐标系中
        self.original_subtitle_region = [0, 0, 0, 0]  # 在原始视频坐标系中
        self.is_selecting = False
        self.selection_start = None

        # 处理状态
        self.is_processing = False
        self.processing_thread = None
        self.result_queue = queue.Queue()

        self.setup_ui()
        self.check_queue()
    
    def setup_ui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="视频字幕变化检测工具",
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))

        # 创建左右分栏布局
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧面板 - 配置和控制
        left_panel = ttk.Frame(content_frame)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # 右侧面板 - 预览和结果
        right_panel = ttk.Frame(content_frame)
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 左侧内容
        self.create_file_section(left_panel)
        self.create_config_section(left_panel)
        self.create_control_section(left_panel)
        self.create_progress_section(left_panel)

        # 右侧内容
        self.create_preview_section(right_panel)
        self.create_result_section(right_panel)
    
    def create_file_section(self, parent):
        # 文件选择框架
        file_frame = ttk.LabelFrame(parent, text="1. 选择视频文件", padding="10")
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 视频文件选择
        video_frame = ttk.Frame(file_frame)
        video_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(video_frame, text="视频文件:").pack(side=tk.LEFT)
        ttk.Entry(video_frame, textvariable=self.video_path, width=50).pack(side=tk.LEFT, padx=(5, 5), fill=tk.X, expand=True)
        ttk.Button(video_frame, text="浏览", command=self.browse_video).pack(side=tk.RIGHT)
        
        # 输出目录选择
        output_frame = ttk.Frame(file_frame)
        output_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(output_frame, text="输出目录:").pack(side=tk.LEFT)
        ttk.Entry(output_frame, textvariable=self.output_dir, width=50).pack(side=tk.LEFT, padx=(5, 5), fill=tk.X, expand=True)
        ttk.Button(output_frame, text="浏览", command=self.browse_output).pack(side=tk.RIGHT)
    
    def create_config_section(self, parent):
        # 配置参数框架
        config_frame = ttk.LabelFrame(parent, text="2. 检测配置", padding="10")
        config_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 创建两列布局
        left_frame = ttk.Frame(config_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        right_frame = ttk.Frame(config_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 左列配置
        ttk.Label(left_frame, text="画面变化阈值:").grid(row=0, column=0, sticky='w', pady=2)
        threshold_frame = ttk.Frame(left_frame)
        threshold_frame.grid(row=0, column=1, sticky='w', padx=(5, 0), pady=2)
        ttk.Entry(threshold_frame, textvariable=self.frame_threshold, width=10).pack(side=tk.LEFT)
        ttk.Label(threshold_frame, text="(默认: 5000)").pack(side=tk.LEFT, padx=(5, 0))
        
        ttk.Label(left_frame, text="字幕区域高度:").grid(row=1, column=0, sticky='w', pady=2)
        height_frame = ttk.Frame(left_frame)
        height_frame.grid(row=1, column=1, sticky='w', padx=(5, 0), pady=2)
        ttk.Entry(height_frame, textvariable=self.subtitle_height, width=10).pack(side=tk.LEFT)
        ttk.Label(height_frame, text="像素 (从底部)").pack(side=tk.LEFT, padx=(5, 0))
        
        ttk.Label(left_frame, text="视频帧率:").grid(row=2, column=0, sticky='w', pady=2)
        fps_frame = ttk.Frame(left_frame)
        fps_frame.grid(row=2, column=1, sticky='w', padx=(5, 0), pady=2)
        ttk.Entry(fps_frame, textvariable=self.fps, width=10).pack(side=tk.LEFT)
        ttk.Label(fps_frame, text="FPS").pack(side=tk.LEFT, padx=(5, 0))
        
        # 右列配置
        ttk.Label(right_frame, text="跳帧间隔:").grid(row=0, column=0, sticky='w', pady=2)
        skip_frame = ttk.Frame(right_frame)
        skip_frame.grid(row=0, column=1, sticky='w', padx=(5, 0), pady=2)
        ttk.Entry(skip_frame, textvariable=self.skip_frames, width=10).pack(side=tk.LEFT)
        ttk.Label(skip_frame, text="(1=每帧检测)").pack(side=tk.LEFT, padx=(5, 0))
        
        # OCR开关
        ttk.Checkbutton(right_frame, text="启用字幕OCR检测", 
                       variable=self.enable_ocr).grid(row=1, column=0, columnspan=2, sticky='w', pady=2)
        
        # 配置说明
        info_text = ("提示: 画面变化阈值越小越敏感；字幕区域高度指从视频底部向上的像素数；\n"
                    "跳帧间隔可以提高处理速度，但可能错过快速变化。")
        ttk.Label(config_frame, text=info_text, font=('Arial', 9), foreground='gray').pack(pady=(10, 0))
    
    def create_control_section(self, parent):
        # 控制按钮框架
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.start_button = ttk.Button(control_frame, text="开始检测",
                                      command=self.start_detection)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(control_frame, text="停止检测", 
                                     command=self.stop_detection, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.clear_button = ttk.Button(control_frame, text="清空结果", 
                                      command=self.clear_results)
        self.clear_button.pack(side=tk.LEFT)
    
    def create_progress_section(self, parent):
        # 进度显示框架
        progress_frame = ttk.LabelFrame(parent, text="3. 检测进度", padding="10")
        progress_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.progress_var = tk.StringVar(value="就绪")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.pack(anchor='w')
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))
    
    def create_result_section(self, parent):
        # 结果显示框架
        result_frame = ttk.LabelFrame(parent, text="4. 检测结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本框和滚动条
        text_frame = ttk.Frame(result_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = tk.Text(text_frame, wrap=tk.WORD, font=('Consolas', 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 导出按钮
        export_frame = ttk.Frame(result_frame)
        export_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(export_frame, text="导出结果", command=self.export_results).pack(side=tk.RIGHT)

    def create_preview_section(self, parent):
        # 视频预览框架
        preview_frame = ttk.LabelFrame(parent, text="视频预览与字幕区域选择", padding="10")
        preview_frame.pack(fill=tk.X, pady=(0, 10))

        # 预览画布
        self.preview_canvas = tk.Canvas(preview_frame, bg='black', height=self.preview_height, width=640)
        self.preview_canvas.pack(fill=tk.X, pady=(0, 10))

        # 绑定鼠标事件用于选择字幕区域
        self.preview_canvas.bind("<Button-1>", self.on_canvas_click)
        self.preview_canvas.bind("<B1-Motion>", self.on_canvas_drag)
        self.preview_canvas.bind("<ButtonRelease-1>", self.on_canvas_release)

        # 预览控制按钮
        preview_control_frame = ttk.Frame(preview_frame)
        preview_control_frame.pack(fill=tk.X)

        ttk.Button(preview_control_frame, text="刷新预览",
                  command=self.refresh_preview).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(preview_control_frame, text="重置字幕区域",
                  command=self.reset_subtitle_region).pack(side=tk.LEFT, padx=(0, 5))

        # 字幕区域信息显示
        self.region_info_var = tk.StringVar(value="字幕区域: 未设置")
        ttk.Label(preview_control_frame, textvariable=self.region_info_var).pack(side=tk.RIGHT)

        # 使用说明
        info_text = ("使用说明: 在视频预览中拖拽鼠标选择字幕区域，红色框表示当前选择的字幕检测区域")
        ttk.Label(preview_frame, text=info_text, font=('Arial', 9),
                 foreground='gray').pack(pady=(10, 0))
    
    def browse_video(self):
        filetypes = (
            ("视频文件", "*.mp4 *.avi *.mov *.mkv *.flv *.wmv"),
            ("所有文件", "*.*")
        )
        filename = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=filetypes
        )
        if filename:
            self.video_path.set(filename)
            # 自动检测视频帧率
            self.detect_video_fps(filename)
            # 加载视频预览
            self.load_video_preview(filename)
    
    def browse_output(self):
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_dir.set(directory)
    
    def detect_video_fps(self, video_path):
        """自动检测视频帧率"""
        try:
            cap = cv2.VideoCapture(video_path)
            if cap.isOpened():
                fps = cap.get(cv2.CAP_PROP_FPS)
                if fps > 0:
                    self.fps.set(round(fps, 2))
                cap.release()
        except Exception as e:
            print(f"检测帧率失败: {e}")

    def load_video_preview(self, video_path):
        """加载视频预览"""
        try:
            # 释放之前的视频
            if self.video_cap:
                self.video_cap.release()

            # 打开新视频
            self.video_cap = cv2.VideoCapture(video_path)
            if not self.video_cap.isOpened():
                messagebox.showerror("错误", "无法打开视频文件")
                return

            # 读取第一帧
            ret, frame = self.video_cap.read()
            if not ret:
                messagebox.showerror("错误", "无法读取视频帧")
                return

            self.current_frame = frame

            # 计算缩放比例和预览尺寸
            original_height, original_width = frame.shape[:2]
            self.scale_factor = self.preview_height / original_height
            self.preview_width = int(original_width * self.scale_factor)

            # 更新画布尺寸
            self.preview_canvas.config(width=self.preview_width, height=self.preview_height)

            # 设置默认字幕区域（底部区域）
            default_height = int(self.subtitle_height.get() * self.scale_factor)
            self.subtitle_region = [
                0,
                self.preview_height - default_height,
                self.preview_width,
                self.preview_height
            ]

            # 更新原始坐标系中的字幕区域
            self.update_original_subtitle_region()

            # 显示预览
            self.display_preview()

        except Exception as e:
            messagebox.showerror("错误", f"加载视频预览失败: {str(e)}")

    def display_preview(self):
        """显示视频预览"""
        if self.current_frame is None or self.preview_width <= 0:
            return

        try:
            # 缩放帧到预览尺寸
            resized_frame = cv2.resize(self.current_frame, (self.preview_width, self.preview_height))

            # 转换颜色空间
            frame_rgb = cv2.cvtColor(resized_frame, cv2.COLOR_BGR2RGB)

            # 转换为PIL图像
            pil_image = Image.fromarray(frame_rgb)

            # 转换为Tkinter可用的格式
            self.preview_photo = ImageTk.PhotoImage(pil_image)

            # 更新画布尺寸
            self.preview_canvas.config(width=self.preview_width, height=self.preview_height)

            # 清空画布并显示图像
            self.preview_canvas.delete("all")
            self.preview_canvas.create_image(
                self.preview_width // 2,
                self.preview_height // 2,
                image=self.preview_photo
            )

            # 绘制字幕区域框
            self.draw_subtitle_region()

        except Exception as e:
            print(f"显示预览失败: {e}")
            messagebox.showerror("错误", f"显示预览失败: {str(e)}")

    def draw_subtitle_region(self):
        """绘制字幕区域选择框"""
        if len(self.subtitle_region) == 4:
            x1, y1, x2, y2 = self.subtitle_region
            self.preview_canvas.create_rectangle(
                x1, y1, x2, y2,
                outline='red', width=2, tags="subtitle_region"
            )

            # 更新区域信息显示
            original_region = self.original_subtitle_region
            if len(original_region) == 4:
                width = original_region[2] - original_region[0]
                height = original_region[3] - original_region[1]
                self.region_info_var.set(f"字幕区域: {width}x{height} 像素")

    def update_original_subtitle_region(self):
        """更新原始坐标系中的字幕区域"""
        if len(self.subtitle_region) == 4 and self.scale_factor > 0:
            x1, y1, x2, y2 = self.subtitle_region
            self.original_subtitle_region = [
                int(x1 / self.scale_factor),
                int(y1 / self.scale_factor),
                int(x2 / self.scale_factor),
                int(y2 / self.scale_factor)
            ]

            # 更新字幕高度参数
            height = self.original_subtitle_region[3] - self.original_subtitle_region[1]
            if height > 0:
                self.subtitle_height.set(height)

    def on_canvas_click(self, event):
        """处理画布点击事件"""
        self.is_selecting = True
        self.selection_start = (event.x, event.y)
        # 清除之前的选择框
        self.preview_canvas.delete("subtitle_region")

    def on_canvas_drag(self, event):
        """处理画布拖拽事件"""
        if self.is_selecting and self.selection_start and self.preview_width > 0:
            # 清除之前的临时选择框
            self.preview_canvas.delete("temp_selection")

            # 绘制临时选择框
            x1, y1 = self.selection_start
            x2, y2 = event.x, event.y

            # 确保坐标在画布范围内
            x1 = max(0, min(x1, self.preview_width))
            y1 = max(0, min(y1, self.preview_height))
            x2 = max(0, min(x2, self.preview_width))
            y2 = max(0, min(y2, self.preview_height))

            self.preview_canvas.create_rectangle(
                x1, y1, x2, y2,
                outline='yellow', width=2, tags="temp_selection"
            )

    def on_canvas_release(self, event):
        """处理画布释放事件"""
        if self.is_selecting and self.selection_start and self.preview_width > 0:
            # 清除临时选择框
            self.preview_canvas.delete("temp_selection")

            # 计算最终选择区域
            x1, y1 = self.selection_start
            x2, y2 = event.x, event.y

            # 确保坐标在画布范围内
            x1 = max(0, min(x1, self.preview_width))
            y1 = max(0, min(y1, self.preview_height))
            x2 = max(0, min(x2, self.preview_width))
            y2 = max(0, min(y2, self.preview_height))

            # 确保x1 < x2, y1 < y2
            if x1 > x2:
                x1, x2 = x2, x1
            if y1 > y2:
                y1, y2 = y2, y1

            # 只有当选择区域有效时才更新
            if abs(x2 - x1) > 5 and abs(y2 - y1) > 5:  # 最小选择区域
                self.subtitle_region = [x1, y1, x2, y2]
                self.update_original_subtitle_region()

                # 重新绘制选择框
                self.draw_subtitle_region()

        self.is_selecting = False
        self.selection_start = None

    def refresh_preview(self):
        """刷新预览"""
        if self.video_path.get() and os.path.exists(self.video_path.get()):
            self.load_video_preview(self.video_path.get())

    def reset_subtitle_region(self):
        """重置字幕区域到默认位置"""
        if self.current_frame is not None:
            # 设置默认字幕区域（底部区域）
            default_height = int(120 * self.scale_factor)  # 默认120像素高度
            self.subtitle_region = [
                0,
                self.preview_height - default_height,
                self.preview_width,
                self.preview_height
            ]

            # 更新原始坐标系中的字幕区域
            self.update_original_subtitle_region()

            # 重新显示预览
            self.display_preview()

    def start_detection(self):
        """开始检测"""
        if not self.video_path.get():
            messagebox.showwarning("警告", "请先选择视频文件")
            return

        if not os.path.exists(self.video_path.get()):
            messagebox.showerror("错误", "视频文件不存在")
            return

        if not os.path.exists(self.output_dir.get()):
            messagebox.showerror("错误", "输出目录不存在")
            return

        # 更新UI状态
        self.is_processing = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_bar.start()
        self.progress_var.set("正在初始化...")

        # 清空结果
        self.result_text.delete(1.0, tk.END)

        # 启动检测线程
        self.processing_thread = threading.Thread(target=self.detection_worker, daemon=True)
        self.processing_thread.start()

    def stop_detection(self):
        """停止检测"""
        self.is_processing = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_bar.stop()
        self.progress_var.set("检测已停止")

    def clear_results(self):
        """清空结果"""
        self.result_text.delete(1.0, tk.END)
        self.progress_var.set("就绪")

    def frame_to_timecode(self, frame_number, fps):
        """将帧号转换为时间码格式 HH:MM:SS:FF"""
        total_seconds = frame_number / fps
        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) // 60)
        seconds = int(total_seconds % 60)
        frames = int(frame_number % fps)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}:{frames:02d}"

    def is_new_subtitle(self, prev_frame, curr_frame, subtitle_region):
        """检测字幕是否发生变化"""
        if not self.enable_ocr.get():
            return False

        try:
            # 提取字幕区域
            prev_sub_img = prev_frame[subtitle_region[0]:subtitle_region[1],
                                    subtitle_region[2]:subtitle_region[3]]
            curr_sub_img = curr_frame[subtitle_region[0]:subtitle_region[1],
                                    subtitle_region[2]:subtitle_region[3]]

            # OCR识别
            prev_text = pytesseract.image_to_string(
                cv2.cvtColor(prev_sub_img, cv2.COLOR_BGR2GRAY),
                config='--psm 6'
            )
            curr_text = pytesseract.image_to_string(
                cv2.cvtColor(curr_sub_img, cv2.COLOR_BGR2GRAY),
                config='--psm 6'
            )

            return prev_text.strip() != curr_text.strip()
        except Exception:
            # OCR失败时返回False
            return False

    def frame_diff(self, prev_gray, curr_gray, threshold):
        """检测帧差异"""
        diff = cv2.absdiff(curr_gray, prev_gray)
        non_zero_count = cv2.countNonZero(diff)
        return non_zero_count > threshold

    def detection_worker(self):
        """检测工作线程"""
        try:
            video_path = self.video_path.get()
            cap = cv2.VideoCapture(video_path)

            if not cap.isOpened():
                self.result_queue.put(("error", "无法打开视频文件"))
                return

            # 获取视频信息
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = self.fps.get()
            frame_threshold = self.frame_threshold.get()
            subtitle_height = self.subtitle_height.get()
            skip_frames = self.skip_frames.get()

            # 读取第一帧
            ret, prev_frame = cap.read()
            if not ret:
                self.result_queue.put(("error", "无法读取视频帧"))
                return

            # 使用用户选择的字幕区域，如果没有选择则使用默认区域
            if len(self.original_subtitle_region) == 4 and any(self.original_subtitle_region):
                x1, y1, x2, y2 = self.original_subtitle_region
                subtitle_region = (y1, y2, x1, x2)  # (y1, y2, x1, x2)
            else:
                # 默认字幕区域 (y1, y2, x1, x2)
                subtitle_region = (
                    prev_frame.shape[0] - subtitle_height,
                    prev_frame.shape[0],
                    0,
                    prev_frame.shape[1]
                )

            prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)

            # 初始化结果统计
            results = []
            frame_count = 0
            processed_count = 0

            self.result_queue.put(("info", f"开始检测视频: {os.path.basename(video_path)}"))
            self.result_queue.put(("info", f"总帧数: {total_frames}, 帧率: {fps} FPS"))
            self.result_queue.put(("info", f"字幕区域: 底部 {subtitle_height} 像素"))
            self.result_queue.put(("info", "=" * 60))

            # 逐帧处理
            while self.is_processing:
                ret, frame = cap.read()
                if not ret:
                    break

                frame_count += 1

                # 跳帧处理
                if frame_count % skip_frames != 0:
                    continue

                processed_count += 1
                curr_gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

                # 检测画面变化
                big_change = self.frame_diff(prev_gray, curr_gray, frame_threshold)

                # 检测字幕变化
                subtitle_change = False
                if self.enable_ocr.get():
                    subtitle_change = self.is_new_subtitle(prev_frame, frame, subtitle_region)

                # 生成时间码
                timecode = self.frame_to_timecode(frame_count, fps)

                # 记录结果
                if big_change:
                    result_text = f"[{timecode}] 画面变化明显"
                    results.append((timecode, "画面变化", frame_count))
                    self.result_queue.put(("result", result_text))
                elif subtitle_change:
                    result_text = f"[{timecode}] 字幕发生变化，但画面基本稳定"
                    results.append((timecode, "字幕变化", frame_count))
                    self.result_queue.put(("result", result_text))
                else:
                    result_text = f"[{timecode}] 无明显变化"
                    # 不记录无变化的帧到结果中，只显示
                    if processed_count % 100 == 0:  # 每100帧显示一次进度
                        self.result_queue.put(("progress", result_text))

                # 更新进度
                progress = (frame_count / total_frames) * 100
                self.result_queue.put(("progress_update", f"处理进度: {progress:.1f}% ({frame_count}/{total_frames})"))

                prev_frame = frame
                prev_gray = curr_gray

            cap.release()

            # 处理完成
            if self.is_processing:
                self.result_queue.put(("info", "=" * 60))
                self.result_queue.put(("info", f"检测完成! 共处理 {processed_count} 帧"))
                self.result_queue.put(("info", f"发现 {len(results)} 个变化点"))

                # 保存结果到文件
                self.save_results_to_file(results, video_path, fps)

                self.result_queue.put(("complete", "检测完成"))

        except Exception as e:
            self.result_queue.put(("error", f"检测过程中发生错误: {str(e)}"))
        finally:
            self.is_processing = False

    def save_results_to_file(self, results, video_path, fps):
        """保存结果到文件"""
        try:
            video_name = os.path.splitext(os.path.basename(video_path))[0]
            output_file = os.path.join(self.output_dir.get(), f"{video_name}_detection_results.txt")

            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"视频字幕变化检测结果\n")
                f.write(f"=" * 50 + "\n")
                f.write(f"视频文件: {video_path}\n")
                f.write(f"检测时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"视频帧率: {fps} FPS\n")
                f.write(f"画面变化阈值: {self.frame_threshold.get()}\n")
                f.write(f"字幕区域高度: {self.subtitle_height.get()} 像素\n")
                f.write(f"OCR检测: {'启用' if self.enable_ocr.get() else '禁用'}\n")
                f.write(f"跳帧间隔: {self.skip_frames.get()}\n")
                f.write(f"总变化点数: {len(results)}\n")
                f.write(f"\n检测结果:\n")
                f.write(f"时间码格式: HH:MM:SS:FF (最后两位为帧号)\n")
                f.write(f"-" * 50 + "\n")

                for timecode, change_type, frame_num in results:
                    f.write(f"{timecode} - {change_type} (第{frame_num}帧)\n")

            self.result_queue.put(("info", f"结果已保存到: {output_file}"))

        except Exception as e:
            self.result_queue.put(("error", f"保存结果文件失败: {str(e)}"))

    def export_results(self):
        """导出当前显示的结果"""
        content = self.result_text.get(1.0, tk.END).strip()
        if not content:
            messagebox.showwarning("警告", "没有可导出的结果")
            return

        filetypes = (
            ("文本文件", "*.txt"),
            ("所有文件", "*.*")
        )
        filename = filedialog.asksaveasfilename(
            title="导出检测结果",
            defaultextension=".txt",
            filetypes=filetypes
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("成功", f"结果已导出到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {str(e)}")

    def check_queue(self):
        """检查队列中的消息并更新UI"""
        try:
            while True:
                msg_type, msg_content = self.result_queue.get_nowait()

                if msg_type == "error":
                    self.result_text.insert(tk.END, f"❌ 错误: {msg_content}\n")
                    self.result_text.see(tk.END)
                    self.stop_detection()
                    messagebox.showerror("错误", msg_content)

                elif msg_type == "info":
                    self.result_text.insert(tk.END, f"ℹ️ {msg_content}\n")
                    self.result_text.see(tk.END)

                elif msg_type == "result":
                    self.result_text.insert(tk.END, f"🔍 {msg_content}\n")
                    self.result_text.see(tk.END)

                elif msg_type == "progress":
                    # 进度信息不显示在结果中，只更新状态
                    pass

                elif msg_type == "progress_update":
                    self.progress_var.set(msg_content)

                elif msg_type == "complete":
                    self.stop_detection()
                    self.progress_var.set("检测完成")
                    messagebox.showinfo("完成", "视频检测已完成！")

        except queue.Empty:
            pass

        # 继续检查队列
        self.master.after(100, self.check_queue)


def main():
    root = tk.Tk()
    VideoSubtitleDetectorGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
