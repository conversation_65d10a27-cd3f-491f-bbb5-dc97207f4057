import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import os
import threading
import time
import queue
from PIL import Image, ImageOps
import json
import numpy as np

class VideoToAPNGBatch:
    def __init__(self, master):
        self.master = master
        master.title("批量视频转APNG工具")
        master.geometry("1000x700")
        master.resizable(True, True)
        
        # 初始化变量
        self.video_files = []  # 存储选择的视频文件列表
        self.output_dir = tk.StringVar(value=os.getcwd())
        
        # APNG转换参数
        self.apng_fps = tk.DoubleVar(value=10.0)
        self.apng_width = tk.IntVar(value=480)
        self.apng_height = tk.IntVar(value=270)
        self.apng_quality = tk.IntVar(value=80)
        self.apng_start_time = tk.DoubleVar(value=0.0)
        self.apng_duration = tk.DoubleVar(value=5.0)
        self.use_original_size = tk.BooleanVar(value=False)
        self.convert_full_video = tk.BooleanVar(value=False)

        # 高级压缩参数
        self.compress_level = tk.IntVar(value=6)  # PNG压缩级别 0-9
        self.color_count = tk.IntVar(value=256)   # 调色板颜色数量
        self.use_palette = tk.BooleanVar(value=False)  # 使用调色板模式
        self.optimize_transparency = tk.BooleanVar(value=True)  # 优化透明度
        self.frame_skip = tk.IntVar(value=1)      # 跳帧间隔
        self.enable_dithering = tk.BooleanVar(value=True)  # 启用抖动
        
        # 处理状态
        self.is_converting = False
        self.current_file_index = 0
        self.total_files = 0
        self.processing_thread = None
        self.result_queue = queue.Queue()
        
        self.setup_ui()
        self.check_queue()
    
    def setup_ui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="批量视频转APNG工具", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 创建左右分栏布局
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧面板 - 配置
        left_panel = ttk.Frame(content_frame)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 右侧面板 - 文件列表和结果
        right_panel = ttk.Frame(content_frame)
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 左侧内容
        self.create_file_section(left_panel)
        self.create_config_section(left_panel)
        self.create_control_section(left_panel)
        self.create_progress_section(left_panel)
        
        # 右侧内容
        self.create_file_list_section(right_panel)
        self.create_result_section(right_panel)
    
    def create_file_section(self, parent):
        # 文件选择框架
        file_frame = ttk.LabelFrame(parent, text="1. 选择视频文件", padding="10")
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件选择按钮
        button_frame = ttk.Frame(file_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(button_frame, text="添加视频文件", 
                  command=self.add_video_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="添加文件夹", 
                  command=self.add_video_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空列表", 
                  command=self.clear_file_list).pack(side=tk.LEFT)
        
        # 输出目录选择
        output_frame = ttk.Frame(file_frame)
        output_frame.pack(fill=tk.X)
        
        ttk.Label(output_frame, text="输出目录:").pack(side=tk.LEFT)
        ttk.Entry(output_frame, textvariable=self.output_dir, width=30).pack(side=tk.LEFT, padx=(5, 5), fill=tk.X, expand=True)
        ttk.Button(output_frame, text="浏览", command=self.browse_output).pack(side=tk.RIGHT)
    
    def create_config_section(self, parent):
        # 配置参数框架
        config_frame = ttk.LabelFrame(parent, text="2. 转换配置", padding="10")
        config_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 基本参数
        basic_frame = ttk.Frame(config_frame)
        basic_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(basic_frame, text="输出帧率:").grid(row=0, column=0, sticky='w', pady=2)
        fps_frame = ttk.Frame(basic_frame)
        fps_frame.grid(row=0, column=1, sticky='w', padx=(5, 0), pady=2)
        ttk.Entry(fps_frame, textvariable=self.apng_fps, width=8).pack(side=tk.LEFT)
        ttk.Label(fps_frame, text="FPS").pack(side=tk.LEFT, padx=(5, 0))
        
        ttk.Label(basic_frame, text="图像质量:").grid(row=1, column=0, sticky='w', pady=2)
        quality_frame = ttk.Frame(basic_frame)
        quality_frame.grid(row=1, column=1, sticky='w', padx=(5, 0), pady=2)
        ttk.Entry(quality_frame, textvariable=self.apng_quality, width=8).pack(side=tk.LEFT)
        ttk.Label(quality_frame, text="%").pack(side=tk.LEFT, padx=(5, 0))
        
        # 尺寸设置
        size_frame = ttk.Frame(config_frame)
        size_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(size_frame, text="使用原始尺寸", 
                       variable=self.use_original_size,
                       command=self.toggle_size_controls).pack(anchor='w')
        
        self.custom_size_frame = ttk.Frame(size_frame)
        self.custom_size_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(self.custom_size_frame, text="宽度:").grid(row=0, column=0, sticky='w', pady=2)
        ttk.Entry(self.custom_size_frame, textvariable=self.apng_width, width=8).grid(row=0, column=1, padx=(5, 10), pady=2)
        ttk.Label(self.custom_size_frame, text="高度:").grid(row=0, column=2, sticky='w', pady=2)
        ttk.Entry(self.custom_size_frame, textvariable=self.apng_height, width=8).grid(row=0, column=3, padx=(5, 0), pady=2)
        
        # 预设按钮
        preset_frame = ttk.Frame(self.custom_size_frame)
        preset_frame.grid(row=1, column=0, columnspan=4, sticky='w', pady=(5, 0))
        
        ttk.Label(preset_frame, text="预设:").pack(side=tk.LEFT)
        ttk.Button(preset_frame, text="480p", command=lambda: self.set_preset(480, 270)).pack(side=tk.LEFT, padx=(5, 2))
        ttk.Button(preset_frame, text="720p", command=lambda: self.set_preset(720, 405)).pack(side=tk.LEFT, padx=(2, 2))
        ttk.Button(preset_frame, text="1080p", command=lambda: self.set_preset(1080, 607)).pack(side=tk.LEFT, padx=(2, 5))
        
        # 时间范围设置
        time_frame = ttk.Frame(config_frame)
        time_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(time_frame, text="转换完整视频", 
                       variable=self.convert_full_video,
                       command=self.toggle_time_controls).pack(anchor='w')
        
        self.custom_time_frame = ttk.Frame(time_frame)
        self.custom_time_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(self.custom_time_frame, text="开始时间:").grid(row=0, column=0, sticky='w', pady=2)
        ttk.Entry(self.custom_time_frame, textvariable=self.apng_start_time, width=8).grid(row=0, column=1, padx=(5, 10), pady=2)
        ttk.Label(self.custom_time_frame, text="持续时间:").grid(row=0, column=2, sticky='w', pady=2)
        ttk.Entry(self.custom_time_frame, textvariable=self.apng_duration, width=8).grid(row=0, column=3, padx=(5, 0), pady=2)
        
        # 高级压缩设置
        advanced_frame = ttk.LabelFrame(config_frame, text="高级压缩设置", padding="5")
        advanced_frame.pack(fill=tk.X, pady=(10, 0))

        # 第一行：压缩级别和跳帧
        row1_frame = ttk.Frame(advanced_frame)
        row1_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(row1_frame, text="压缩级别:").grid(row=0, column=0, sticky='w', pady=2)
        compress_frame = ttk.Frame(row1_frame)
        compress_frame.grid(row=0, column=1, sticky='w', padx=(5, 15), pady=2)
        ttk.Scale(compress_frame, from_=0, to=9, variable=self.compress_level,
                 orient=tk.HORIZONTAL, length=80).pack(side=tk.LEFT)
        ttk.Label(compress_frame, textvariable=self.compress_level, width=2).pack(side=tk.LEFT, padx=(5, 0))

        ttk.Label(row1_frame, text="跳帧间隔:").grid(row=0, column=2, sticky='w', pady=2)
        skip_frame = ttk.Frame(row1_frame)
        skip_frame.grid(row=0, column=3, sticky='w', padx=(5, 0), pady=2)
        ttk.Entry(skip_frame, textvariable=self.frame_skip, width=6).pack(side=tk.LEFT)
        ttk.Label(skip_frame, text="帧").pack(side=tk.LEFT, padx=(2, 0))

        # 第二行：调色板设置
        row2_frame = ttk.Frame(advanced_frame)
        row2_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Checkbutton(row2_frame, text="使用调色板",
                       variable=self.use_palette,
                       command=self.toggle_palette_controls).pack(side=tk.LEFT)

        self.palette_frame = ttk.Frame(row2_frame)
        self.palette_frame.pack(side=tk.LEFT, padx=(10, 0))

        ttk.Label(self.palette_frame, text="颜色数:").pack(side=tk.LEFT)
        color_scale = ttk.Scale(self.palette_frame, from_=16, to=256, variable=self.color_count,
                               orient=tk.HORIZONTAL, length=100)
        color_scale.pack(side=tk.LEFT, padx=(5, 5))
        ttk.Label(self.palette_frame, textvariable=self.color_count, width=3).pack(side=tk.LEFT)

        # 第三行：其他优化选项
        row3_frame = ttk.Frame(advanced_frame)
        row3_frame.pack(fill=tk.X)

        ttk.Checkbutton(row3_frame, text="启用抖动",
                       variable=self.enable_dithering).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Checkbutton(row3_frame, text="优化透明度",
                       variable=self.optimize_transparency).pack(side=tk.LEFT)

        # 初始化调色板控件状态
        self.toggle_palette_controls()

        # 压缩预设按钮
        preset_compress_frame = ttk.Frame(advanced_frame)
        preset_compress_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(preset_compress_frame, text="压缩预设:").pack(side=tk.LEFT)
        ttk.Button(preset_compress_frame, text="高质量",
                  command=self.set_quality_preset).pack(side=tk.LEFT, padx=(5, 2))
        ttk.Button(preset_compress_frame, text="平衡",
                  command=self.set_balanced_preset).pack(side=tk.LEFT, padx=(2, 2))
        ttk.Button(preset_compress_frame, text="高压缩",
                  command=self.set_compress_preset).pack(side=tk.LEFT, padx=(2, 5))

        # 配置保存/加载
        config_save_frame = ttk.Frame(config_frame)
        config_save_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(config_save_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(config_save_frame, text="加载配置", command=self.load_config).pack(side=tk.LEFT)
    
    def create_control_section(self, parent):
        # 控制按钮框架
        control_frame = ttk.LabelFrame(parent, text="3. 批量转换控制", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)
        
        self.start_button = ttk.Button(button_frame, text="开始批量转换", 
                                      command=self.start_batch_conversion)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="停止转换", 
                                     command=self.stop_conversion, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.clear_button = ttk.Button(button_frame, text="清空结果", 
                                      command=self.clear_results)
        self.clear_button.pack(side=tk.LEFT)
    
    def create_progress_section(self, parent):
        # 进度显示框架
        progress_frame = ttk.LabelFrame(parent, text="4. 转换进度", padding="10")
        progress_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 总体进度
        self.overall_progress_var = tk.StringVar(value="就绪")
        self.overall_progress_label = ttk.Label(progress_frame, textvariable=self.overall_progress_var)
        self.overall_progress_label.pack(anchor='w')
        
        self.overall_progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.overall_progress_bar.pack(fill=tk.X, pady=(5, 10))
        
        # 当前文件进度
        self.current_progress_var = tk.StringVar(value="")
        self.current_progress_label = ttk.Label(progress_frame, textvariable=self.current_progress_var)
        self.current_progress_label.pack(anchor='w')
        
        self.current_progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.current_progress_bar.pack(fill=tk.X, pady=(5, 0))
    
    def create_file_list_section(self, parent):
        # 文件列表框架
        list_frame = ttk.LabelFrame(parent, text="选中的视频文件", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建Treeview来显示文件列表
        columns = ('文件名', '路径', '大小', '时长', '分辨率', '状态')
        self.file_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        # 设置列标题
        for col in columns:
            self.file_tree.heading(col, text=col)
            if col == '文件名':
                self.file_tree.column(col, width=150)
            elif col == '路径':
                self.file_tree.column(col, width=200)
            elif col == '状态':
                self.file_tree.column(col, width=100)
            else:
                self.file_tree.column(col, width=80)
        
        # 添加滚动条
        scrollbar_v = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        scrollbar_h = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.file_tree.xview)
        self.file_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        # 布局
        self.file_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 右键菜单
        self.context_menu = tk.Menu(self.master, tearoff=0)
        self.context_menu.add_command(label="移除选中文件", command=self.remove_selected_files)
        self.context_menu.add_command(label="预览视频信息", command=self.preview_video_info)
        
        self.file_tree.bind("<Button-2>", self.show_context_menu)  # macOS右键
        self.file_tree.bind("<Button-3>", self.show_context_menu)  # Windows/Linux右键
    
    def create_result_section(self, parent):
        # 结果显示框架
        result_frame = ttk.LabelFrame(parent, text="转换结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本框和滚动条
        text_frame = ttk.Frame(result_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = tk.Text(text_frame, wrap=tk.WORD, font=('Consolas', 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 导出按钮
        export_frame = ttk.Frame(result_frame)
        export_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(export_frame, text="导出日志", command=self.export_log).pack(side=tk.LEFT)
        ttk.Button(export_frame, text="打开输出目录", command=self.open_output_dir).pack(side=tk.RIGHT)

    def add_video_files(self):
        """添加视频文件"""
        filetypes = (
            ("视频文件", "*.mp4 *.avi *.mov *.mkv *.flv *.wmv *.m4v *.webm"),
            ("所有文件", "*.*")
        )
        filenames = filedialog.askopenfilenames(
            title="选择视频文件",
            filetypes=filetypes
        )

        for filename in filenames:
            if filename not in [item['path'] for item in self.video_files]:
                self.add_video_to_list(filename)

    def add_video_folder(self):
        """添加文件夹中的所有视频"""
        folder_path = filedialog.askdirectory(title="选择包含视频的文件夹")
        if folder_path:
            video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.m4v', '.webm'}

            for root, _, files in os.walk(folder_path):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in video_extensions):
                        full_path = os.path.join(root, file)
                        if full_path not in [item['path'] for item in self.video_files]:
                            self.add_video_to_list(full_path)

    def add_video_to_list(self, video_path):
        """添加视频到列表并获取信息"""
        try:
            # 获取视频信息
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return

            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            duration = frame_count / fps if fps > 0 else 0

            cap.release()

            # 获取文件大小
            file_size = os.path.getsize(video_path) / (1024 * 1024)  # MB

            # 创建视频信息字典
            video_info = {
                'path': video_path,
                'filename': os.path.basename(video_path),
                'size_mb': file_size,
                'duration': duration,
                'width': width,
                'height': height,
                'fps': fps,
                'status': '待转换'
            }

            self.video_files.append(video_info)

            # 添加到树形视图
            self.file_tree.insert('', 'end', values=(
                video_info['filename'],
                video_info['path'],
                f"{file_size:.1f} MB",
                f"{duration:.1f}s",
                f"{width}x{height}",
                video_info['status']
            ))

        except Exception as e:
            messagebox.showerror("错误", f"添加视频文件失败: {str(e)}")

    def clear_file_list(self):
        """清空文件列表"""
        self.video_files.clear()
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

    def remove_selected_files(self):
        """移除选中的文件"""
        selected_items = self.file_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要移除的文件")
            return

        # 获取选中项的索引
        indices_to_remove = []
        for item in selected_items:
            index = self.file_tree.index(item)
            indices_to_remove.append(index)

        # 按倒序删除，避免索引变化
        for index in sorted(indices_to_remove, reverse=True):
            if 0 <= index < len(self.video_files):
                del self.video_files[index]

        # 删除树形视图中的项
        for item in selected_items:
            self.file_tree.delete(item)

    def preview_video_info(self):
        """预览视频信息"""
        selected_items = self.file_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要预览的文件")
            return

        item = selected_items[0]
        index = self.file_tree.index(item)
        if 0 <= index < len(self.video_files):
            video_info = self.video_files[index]
            info_text = f"""视频信息:
文件名: {video_info['filename']}
路径: {video_info['path']}
文件大小: {video_info['size_mb']:.1f} MB
时长: {video_info['duration']:.1f} 秒
分辨率: {video_info['width']}x{video_info['height']}
帧率: {video_info['fps']:.2f} FPS
状态: {video_info['status']}"""

            messagebox.showinfo("视频信息", info_text)

    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def browse_output(self):
        """选择输出目录"""
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_dir.set(directory)

    def toggle_size_controls(self):
        """切换尺寸控制"""
        if self.use_original_size.get():
            for widget in self.custom_size_frame.winfo_children():
                widget.configure(state='disabled')
        else:
            for widget in self.custom_size_frame.winfo_children():
                if isinstance(widget, ttk.Entry):
                    widget.configure(state='normal')
                elif isinstance(widget, ttk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, (ttk.Button, ttk.Entry)):
                            child.configure(state='normal')

    def toggle_time_controls(self):
        """切换时间控制"""
        if self.convert_full_video.get():
            for widget in self.custom_time_frame.winfo_children():
                if isinstance(widget, ttk.Entry):
                    widget.configure(state='disabled')
        else:
            for widget in self.custom_time_frame.winfo_children():
                if isinstance(widget, ttk.Entry):
                    widget.configure(state='normal')

    def set_preset(self, width, height):
        """设置预设分辨率"""
        if not self.use_original_size.get():
            self.apng_width.set(width)
            self.apng_height.set(height)

    def toggle_palette_controls(self):
        """切换调色板控制"""
        if self.use_palette.get():
            for widget in self.palette_frame.winfo_children():
                if hasattr(widget, 'configure'):
                    widget.configure(state='normal')
        else:
            for widget in self.palette_frame.winfo_children():
                if hasattr(widget, 'configure'):
                    widget.configure(state='disabled')

    def set_quality_preset(self):
        """设置高质量预设"""
        self.compress_level.set(3)
        self.use_palette.set(False)
        self.enable_dithering.set(False)
        self.optimize_transparency.set(True)
        self.frame_skip.set(1)
        self.toggle_palette_controls()

    def set_balanced_preset(self):
        """设置平衡预设"""
        self.compress_level.set(6)
        self.use_palette.set(True)
        self.color_count.set(128)
        self.enable_dithering.set(True)
        self.optimize_transparency.set(True)
        self.frame_skip.set(1)
        self.toggle_palette_controls()

    def set_compress_preset(self):
        """设置高压缩预设"""
        self.compress_level.set(9)
        self.use_palette.set(True)
        self.color_count.set(64)
        self.enable_dithering.set(True)
        self.optimize_transparency.set(True)
        self.frame_skip.set(2)
        self.toggle_palette_controls()

    def save_config(self):
        """保存配置到文件"""
        config = {
            'apng_fps': self.apng_fps.get(),
            'apng_width': self.apng_width.get(),
            'apng_height': self.apng_height.get(),
            'apng_quality': self.apng_quality.get(),
            'apng_start_time': self.apng_start_time.get(),
            'apng_duration': self.apng_duration.get(),
            'use_original_size': self.use_original_size.get(),
            'convert_full_video': self.convert_full_video.get(),
            'output_dir': self.output_dir.get(),
            # 高级压缩参数
            'compress_level': self.compress_level.get(),
            'color_count': self.color_count.get(),
            'use_palette': self.use_palette.get(),
            'optimize_transparency': self.optimize_transparency.get(),
            'frame_skip': self.frame_skip.get(),
            'enable_dithering': self.enable_dithering.get()
        }

        filename = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("成功", "配置已保存")
            except Exception as e:
                messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def load_config(self):
        """从文件加载配置"""
        filename = filedialog.askopenfilename(
            title="加载配置文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 应用配置
                self.apng_fps.set(config.get('apng_fps', 10.0))
                self.apng_width.set(config.get('apng_width', 480))
                self.apng_height.set(config.get('apng_height', 270))
                self.apng_quality.set(config.get('apng_quality', 80))
                self.apng_start_time.set(config.get('apng_start_time', 0.0))
                self.apng_duration.set(config.get('apng_duration', 5.0))
                self.use_original_size.set(config.get('use_original_size', False))
                self.convert_full_video.set(config.get('convert_full_video', False))
                self.output_dir.set(config.get('output_dir', os.getcwd()))

                # 高级压缩参数
                self.compress_level.set(config.get('compress_level', 6))
                self.color_count.set(config.get('color_count', 256))
                self.use_palette.set(config.get('use_palette', False))
                self.optimize_transparency.set(config.get('optimize_transparency', True))
                self.frame_skip.set(config.get('frame_skip', 1))
                self.enable_dithering.set(config.get('enable_dithering', True))

                # 更新UI状态
                self.toggle_size_controls()
                self.toggle_time_controls()
                self.toggle_palette_controls()

                messagebox.showinfo("成功", "配置已加载")
            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {str(e)}")

    def start_batch_conversion(self):
        """开始批量转换"""
        if not self.video_files:
            messagebox.showwarning("警告", "请先添加视频文件")
            return

        if not os.path.exists(self.output_dir.get()):
            messagebox.showerror("错误", "输出目录不存在")
            return

        # 验证参数
        if not self.convert_full_video.get():
            if self.apng_duration.get() <= 0:
                messagebox.showerror("错误", "持续时间必须大于0")
                return

        if self.apng_fps.get() <= 0 or self.apng_fps.get() > 60:
            messagebox.showerror("错误", "帧率必须在0-60之间")
            return

        if not self.use_original_size.get():
            if self.apng_width.get() <= 0 or self.apng_height.get() <= 0:
                messagebox.showerror("错误", "分辨率必须大于0")
                return

        # 更新UI状态
        self.is_converting = True
        self.current_file_index = 0
        self.total_files = len(self.video_files)

        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)

        self.overall_progress_bar.config(maximum=self.total_files, value=0)
        self.current_progress_bar.start()

        # 清空结果
        self.result_text.delete(1.0, tk.END)

        # 重置所有文件状态
        for i, video_info in enumerate(self.video_files):
            video_info['status'] = '待转换'
            # 更新树形视图
            item_id = self.file_tree.get_children()[i]
            values = list(self.file_tree.item(item_id)['values'])
            values[5] = '待转换'  # 状态列
            self.file_tree.item(item_id, values=values)

        # 启动转换线程
        self.processing_thread = threading.Thread(target=self.batch_conversion_worker, daemon=True)
        self.processing_thread.start()

    def stop_conversion(self):
        """停止转换"""
        self.is_converting = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.current_progress_bar.stop()
        self.overall_progress_var.set("转换已停止")
        self.current_progress_var.set("")

    def clear_results(self):
        """清空结果"""
        self.result_text.delete(1.0, tk.END)
        self.overall_progress_var.set("就绪")
        self.current_progress_var.set("")

    def batch_conversion_worker(self):
        """批量转换工作线程"""
        try:
            successful_count = 0
            failed_count = 0

            self.result_queue.put(("info", f"开始批量转换 {self.total_files} 个视频文件"))
            self.result_queue.put(("info", "=" * 60))

            for i, video_info in enumerate(self.video_files):
                if not self.is_converting:
                    break

                self.current_file_index = i
                self.result_queue.put(("progress_overall", f"处理文件 {i+1}/{self.total_files}: {video_info['filename']}"))
                self.result_queue.put(("progress_current", f"正在转换: {video_info['filename']}"))
                self.result_queue.put(("status_update", i, "转换中"))

                try:
                    # 转换单个视频
                    success = self.convert_single_video(video_info)

                    if success:
                        successful_count += 1
                        self.result_queue.put(("status_update", i, "完成"))
                        self.result_queue.put(("info", f"✅ {video_info['filename']} - 转换成功"))
                    else:
                        failed_count += 1
                        self.result_queue.put(("status_update", i, "失败"))
                        self.result_queue.put(("error", f"❌ {video_info['filename']} - 转换失败"))

                except Exception as e:
                    failed_count += 1
                    self.result_queue.put(("status_update", i, "错误"))
                    self.result_queue.put(("error", f"❌ {video_info['filename']} - 错误: {str(e)}"))

                # 更新总体进度
                self.result_queue.put(("progress_bar", i + 1))

            # 转换完成总结
            self.result_queue.put(("info", "=" * 60))
            self.result_queue.put(("info", f"批量转换完成!"))
            self.result_queue.put(("info", f"成功: {successful_count} 个文件"))
            self.result_queue.put(("info", f"失败: {failed_count} 个文件"))
            self.result_queue.put(("info", f"总计: {successful_count + failed_count} 个文件"))

            if self.is_converting:
                self.result_queue.put(("complete", "批量转换完成"))

        except Exception as e:
            self.result_queue.put(("error", f"批量转换过程中发生错误: {str(e)}"))
        finally:
            self.is_converting = False

    def convert_single_video(self, video_info):
        """转换单个视频为APNG"""
        try:
            video_path = video_info['path']
            cap = cv2.VideoCapture(video_path)

            if not cap.isOpened():
                return False

            # 获取视频信息
            original_fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            video_duration = total_frames / original_fps
            original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            # 获取转换参数
            if self.convert_full_video.get():
                start_time = 0.0
                duration = video_duration
            else:
                start_time = self.apng_start_time.get()
                duration = self.apng_duration.get()

            # 验证时间范围
            if start_time >= video_duration:
                return False

            if start_time + duration > video_duration:
                duration = video_duration - start_time

            output_fps = self.apng_fps.get()

            # 确定输出尺寸
            if self.use_original_size.get():
                output_width = original_width
                output_height = original_height
            else:
                output_width = self.apng_width.get()
                output_height = self.apng_height.get()

            # 计算帧范围
            start_frame = int(start_time * original_fps)
            total_output_frames = int(duration * output_fps)

            # 收集帧数据
            frames = []
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)

            frame_interval = original_fps / output_fps
            current_frame_pos = 0
            frame_skip = self.frame_skip.get()

            for frame_idx in range(0, total_output_frames, frame_skip):
                if not self.is_converting:
                    break

                # 计算应该读取的帧位置
                target_frame = start_frame + int(current_frame_pos)
                cap.set(cv2.CAP_PROP_POS_FRAMES, target_frame)

                ret, frame = cap.read()
                if not ret:
                    break

                # 调整帧大小
                if output_width != original_width or output_height != original_height:
                    resized_frame = cv2.resize(frame, (output_width, output_height))
                else:
                    resized_frame = frame

                # 转换颜色空间 BGR -> RGB
                rgb_frame = cv2.cvtColor(resized_frame, cv2.COLOR_BGR2RGB)

                # 转换为PIL图像
                pil_image = Image.fromarray(rgb_frame)

                # 应用压缩优化
                pil_image = self.apply_compression_optimizations(pil_image)

                frames.append(pil_image)

                current_frame_pos += frame_interval * frame_skip

            cap.release()

            if not frames or not self.is_converting:
                return False

            # 生成输出文件名
            video_name = os.path.splitext(video_info['filename'])[0]
            if self.convert_full_video.get():
                output_filename = f"{video_name}_full_{output_fps}fps.png"
            else:
                output_filename = f"{video_name}_{start_time}s-{start_time+duration}s_{output_fps}fps.png"

            output_path = os.path.join(self.output_dir.get(), output_filename)

            # 计算每帧持续时间（毫秒）
            frame_duration = int(1000 / output_fps * frame_skip)

            # 保存APNG with advanced compression
            save_kwargs = {
                'save_all': True,
                'append_images': frames[1:],
                'duration': frame_duration,
                'loop': 0,  # 无限循环
                'optimize': self.optimize_transparency.get(),
                'compress_level': self.compress_level.get()
            }

            frames[0].save(output_path, **save_kwargs)

            return True

        except Exception:
            return False

    def apply_compression_optimizations(self, image):
        """应用压缩优化"""
        try:
            # 如果启用调色板模式
            if self.use_palette.get():
                # 转换为调色板模式以减少颜色数量
                if self.enable_dithering.get():
                    # 使用抖动算法
                    image = image.quantize(
                        colors=self.color_count.get(),
                        method=Image.Quantize.MEDIANCUT,
                        dither=Image.Dither.FLOYDSTEINBERG
                    )
                else:
                    # 不使用抖动
                    image = image.quantize(
                        colors=self.color_count.get(),
                        method=Image.Quantize.MEDIANCUT,
                        dither=Image.Dither.NONE
                    )

                # 转换回RGB模式以保持兼容性
                image = image.convert('RGB')

            return image

        except Exception:
            # 如果优化失败，返回原图像
            return image

    def export_log(self):
        """导出转换日志"""
        content = self.result_text.get(1.0, tk.END).strip()
        if not content:
            messagebox.showwarning("警告", "没有可导出的日志")
            return

        filename = filedialog.asksaveasfilename(
            title="导出转换日志",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"批量视频转APNG日志\n")
                    f.write(f"导出时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"=" * 50 + "\n")
                    f.write(content)
                messagebox.showinfo("成功", f"日志已导出到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {str(e)}")

    def open_output_dir(self):
        """打开输出目录"""
        output_path = self.output_dir.get()
        if os.path.exists(output_path):
            try:
                if os.name == 'nt':  # Windows
                    os.startfile(output_path)
                else:  # macOS/Linux
                    os.system(f'open "{output_path}"')
            except Exception as e:
                messagebox.showerror("错误", f"无法打开目录: {str(e)}")
        else:
            messagebox.showerror("错误", "输出目录不存在")

    def check_queue(self):
        """检查队列中的消息并更新UI"""
        try:
            while True:
                msg_type, *msg_data = self.result_queue.get_nowait()

                if msg_type == "error":
                    self.result_text.insert(tk.END, f"❌ 错误: {msg_data[0]}\n")
                    self.result_text.see(tk.END)

                elif msg_type == "info":
                    self.result_text.insert(tk.END, f"ℹ️ {msg_data[0]}\n")
                    self.result_text.see(tk.END)

                elif msg_type == "progress_overall":
                    self.overall_progress_var.set(msg_data[0])

                elif msg_type == "progress_current":
                    self.current_progress_var.set(msg_data[0])

                elif msg_type == "progress_bar":
                    self.overall_progress_bar.config(value=msg_data[0])

                elif msg_type == "status_update":
                    file_index, status = msg_data[0], msg_data[1]
                    if 0 <= file_index < len(self.video_files):
                        self.video_files[file_index]['status'] = status
                        # 更新树形视图
                        item_id = self.file_tree.get_children()[file_index]
                        values = list(self.file_tree.item(item_id)['values'])
                        values[5] = status  # 状态列
                        self.file_tree.item(item_id, values=values)

                elif msg_type == "complete":
                    self.stop_conversion()
                    self.overall_progress_var.set("批量转换完成")
                    self.current_progress_var.set("")
                    messagebox.showinfo("完成", "批量转换已完成！")

        except queue.Empty:
            pass

        # 继续检查队列
        self.master.after(100, self.check_queue)


def main():
    root = tk.Tk()
    VideoToAPNGBatch(root)
    root.mainloop()


if __name__ == "__main__":
    main()
